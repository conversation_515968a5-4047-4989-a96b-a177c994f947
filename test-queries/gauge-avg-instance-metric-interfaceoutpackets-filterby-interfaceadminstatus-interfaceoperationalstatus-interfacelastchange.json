{"visualization.timeline": {"relative.timeline": "today", "visualization.timezone": "Asia/Calcutta", "from.datetime": 1659378600304, "to.datetime": 1659464999304, "duration": 86399}, "visualization.category": "Gauge", "visualization.type": "Grid", "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "interface~out.packets.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}], "sorting": {"limit": 10, "order": "asc", "column": "interface~out.packets.avg"}}}, "visualization.result.by": [], "granularity": {"value": 5, "unit": "m"}, "id": -1, "ui.event.uuid": "de7f92a8-d413-46d7-a908-a8507530e9eb", "session-id": "e04a5a90-62cd-4723-bcce-46a55f29798a", "user.name": "admin", "visualization.data.sources": {"type": "metric", "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "interface~admin.status^last", "operator": "end with", "value": "up"}, {"operand": "interface~operational.status^last", "operator": "start with", "value": "main"}, {"operand": "interface~last.change^last", "operator": "=", "value": "1 day"}]}, {"filter": "exclude", "operator": "or", "conditions": [{"operand": "interface~admin.status^last", "operator": "contain", "value": "d"}, {"operand": "interface~operational.status^last", "operator": "=", "value": "unknown"}, {"operand": "interface~admin.status^last", "operator": "end with", "value": "n"}]}, {"filter": "include", "operator": "or", "conditions": [{"operand": "interface~operational.status^last", "operator": "contain", "value": "u"}, {"operand": "interface~admin.status^last", "operator": "end with", "value": "p"}]}]}, "result.filter": {}}, "visualization.result.by": [], "data.points": [{"data.point": "interface~out.packets", "aggregator": "avg", "entity.type": "all", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface", "6": "2000-interface", "7": "2000-interface", "8": "2000-interface", "9": "2000-interface", "10": "2000-interface"}, "plugins": ["2000-interface"]}], "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface", "6": "2000-interface", "7": "2000-interface", "8": "2000-interface", "9": "2000-interface", "10": "2000-interface"}, "status": [], "instance.type": "interface", "plugins": ["2000-interface"]}, "admin.role": "yes", "query.id": 116611606211068, "sub.query.id": 116611606211069}