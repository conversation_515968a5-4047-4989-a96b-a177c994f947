/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/*
	With an event aggregator, data can be aggregated online and written as soon as it is received, as opposed to being calculated when a query is run.

	When we receive a request for event aggregation, datareader provides a filename where the previously aggregated data is stored;
	all we need to do is store the newly arrived data. additionally, we notify the datareader when a file is written.

	Unlike metric aggregators, where stores are based on metrics, event aggregators base store names on views created by plugins,
	where we store aggregated data.

	Note :
		- event id generated for posting list is generated based on tick and part of key, it contains both information
		- when error occurs while writing data, then for that interval job will write aggregation.
*/

/*Change Logs:
* 2025-05-05			 <PERSON>wa<PERSON><PERSON>l <PERSON>. Dave		MOTADATA-6078 passed defaultblobpool const as the function parameter in memory pool initialization
* 2025-05-10             Dhaval Bera            MOTADATA-6174  Added Unmap function to release memory-mapped resources
 */

package writer

import (
	"bytes"
	"encoding/binary"
	"errors"
	"fmt"
	"github.com/dolthub/swiss"
	"github.com/kelindar/bitmap"
	"github.com/valyala/gozstd"
	"golang.org/x/sys/unix"
	"math"
	. "motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	"os"
	"runtime"
	"sort"
	"strings"
	"sync"
	"sync/atomic"
)

var (
	workingDir = utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.Aggregations + utils.PathSeparator

	maxKeyElementSize = utils.AggregationViewIndexColumnLimit

	defaultPartIndex = "0"
)

const errorStackTrace = "!!!STACK TRACE for event aggregator %v!!! \n %v"

type INT32Column struct {
	values []int64

	groups []int32
}

func (item INT32Column) Len() int { return len(item.values) }
func (item INT32Column) Less(i, j int) bool {
	return item.values[i] > item.values[j]
}

func (item INT32Column) Swap(i, j int) {
	item.values[i], item.values[j] = item.values[j], item.values[i]
	item.groups[i], item.groups[j] = item.groups[j], item.groups[i]
}

type EventAggregator struct {
	ShutdownNotifications chan bool

	logger utils.Logger

	waitGroup *sync.WaitGroup

	Events chan utils.MotadataMap

	tokenizers []*utils.Tokenizer

	encoder Encoder

	decoder Decoder

	events []storage.DiskIOEventBatch

	event storage.DiskIOEvent

	/* Column index wise list of map

	index := 0

	map[string]map[int64]struct{}

	key --- > map[int64]struct{}

	key ---> string ordinal original value (eg. tcp)

	map[int64]struct{}

	key ----> struct

	key --- > list of all event id's , for example tcp comes in multiple parts than it contains multiple event ID's

	*/
	stringTokens []map[string]map[int64]struct{} // max grouping + condition limit

	numericTokens []map[int64]map[int64]struct{} // max grouping + condition limit

	// Motadata-4316 Earlier pass over string tokens only contains the default part eventId , but now it contains the multiple part indexId so need to restructure the datastructure
	passoverStringTokens []map[string]map[int64]struct{} // max grouping + condition limit

	// Motadata-4316 Earlier pass over numeric tokens only contains the default part eventId , but now it contains the multiple part indexId so need to restructure the datastructure
	passoverNumericTokens []map[int64]map[int64]struct{} // max grouping + condition limit

	valuesByOrdinal *swiss.Map[int32, string]

	ordinalsByValue *swiss.Map[string, int32]

	aggregations *swiss.Map[string, int64]

	stringMappings, numericMappings *swiss.Map[int32, []int]

	groups *swiss.Map[string, int]

	probes *swiss.Map[string, struct{}]

	dataTypes map[string]DataType

	poolIndices map[string]int

	txnEntries map[uint64]utils.TxnEntry

	eventIds map[int64]struct{}

	ordinals *bitmap.Bitmap

	decodedBytes, int64ValueBytes, bufferBytes, txnBufferBytes, int32Bytes []byte

	keyBuffers, valueBuffers, buffers [][]byte

	indexableColumns, aggregationColumns []string

	columns map[string]string

	tick string

	aggregatorId, overflowLength, indexableColumnElementSize,

	aggregationColumnElementSize, txnOffset, txnPartition, records int

	PendingRequests int32

	pending, shutdown bool
}

func NewEventAggregator(aggregatorId int) *EventAggregator {

	pool := utils.NewMemoryPool(14, utils.EventAggregatorPoolLength, true, utils.DefaultBlobPools)

	return &EventAggregator{

		aggregatorId: aggregatorId,

		logger: utils.NewLogger(fmt.Sprintf("Event Aggregator %v", aggregatorId), "aggregator"),

		waitGroup: &sync.WaitGroup{},

		Events: make(chan utils.MotadataMap, 1_00_00),

		valuesByOrdinal: swiss.NewMap[int32, string](uint32(utils.OverflowLength)),

		ordinalsByValue: swiss.NewMap[string, int32](uint32(utils.OverflowLength)),

		aggregations: swiss.NewMap[string, int64](uint32(utils.OverflowLength)),

		stringMappings: swiss.NewMap[int32, []int](uint32(utils.OverflowLength)),

		numericMappings: swiss.NewMap[int32, []int](uint32(utils.OverflowLength)),

		stringTokens: make([]map[string]map[int64]struct{}, maxKeyElementSize), // max grouping + condition limit

		passoverStringTokens: make([]map[string]map[int64]struct{}, maxKeyElementSize), // max grouping + condition limit

		numericTokens: make([]map[int64]map[int64]struct{}, maxKeyElementSize), // max grouping + condition limit

		passoverNumericTokens: make([]map[int64]map[int64]struct{}, maxKeyElementSize), // max grouping + condition limit

		eventIds: make(map[int64]struct{}, 1000),

		encoder: NewEncoder(pool),

		decoder: NewDecoder(pool),

		indexableColumns: make([]string, 20),

		aggregationColumns: make([]string, 100),

		ShutdownNotifications: make(chan bool, 5),

		groups: swiss.NewMap[string, int](uint32(utils.OverflowLength)),

		probes: swiss.NewMap[string, struct{}](uint32(utils.OverflowLength)),

		events: make([]storage.DiskIOEventBatch, utils.MaxStoreParts),

		dataTypes: make(map[string]DataType, 100),

		columns: make(map[string]string, 30),

		poolIndices: make(map[string]int, 100),

		tokenizers: make([]*utils.Tokenizer, 3),

		keyBuffers: make([][]byte, maxKeyElementSize),

		buffers: make([][]byte, maxKeyElementSize),

		valueBuffers: make([][]byte, maxKeyElementSize),

		event: storage.DiskIOEvent{},

		ordinals: &bitmap.Bitmap{0},

		txnEntries: make(map[uint64]utils.TxnEntry, 50),

		txnOffset: 4, //initial padding for the length of bytes

		txnPartition: utils.NotAvailable,

		int32Bytes: make([]byte, 4),
	}
}

func (aggregator *EventAggregator) Start() {

	aggregator.decodedBytes = make([]byte, utils.MaxBlobBytes*3)

	aggregator.int64ValueBytes = make([]byte, utils.AggregationViewIndexColumnLimit)

	for i := range aggregator.events {

		aggregator.events[i] = storage.DiskIOEventBatch{}
	}

	for i := range aggregator.valueBuffers {

		bufferBytes, err := unix.Mmap(-1, 0, utils.MaxValueBufferBytes, unix.PROT_READ|unix.PROT_WRITE, unix.MAP_PRIVATE|unix.MAP_ANON)

		if err != nil {

			aggregator.logger.Error(fmt.Sprintf("error %v occurred while mapping annonymous buffer for event aggregator %v and index %v", err, aggregator.aggregatorId, i))

			bufferBytes = make([]byte, utils.MaxValueBufferBytes)
		}

		aggregator.valueBuffers[i] = bufferBytes
	}

	txnBufferBytes, err := unix.Mmap(-1, 0, utils.DataWriterTxnBufferBytes, unix.PROT_READ|unix.PROT_WRITE, unix.MAP_PRIVATE|unix.MAP_ANON)

	if err != nil {

		aggregator.logger.Error(fmt.Sprintf("error %v occurred while mapping annonymous txn buffer for event aggregator %v", err, aggregator.aggregatorId))

		txnBufferBytes = make([]byte, utils.DataWriterTxnBufferBytes)
	}

	aggregator.txnBufferBytes = txnBufferBytes

	bufferBytes, err := unix.Mmap(-1, 0, utils.DataWriterValueBufferBytes, unix.PROT_READ|unix.PROT_WRITE, unix.MAP_PRIVATE|unix.MAP_ANON)

	if err != nil {

		aggregator.logger.Error(fmt.Sprintf("error %v occurred while mapping annonymous buffer bytes for event aggregator %v", err, aggregator.aggregatorId))

		bufferBytes = make([]byte, utils.DataWriterValueBufferBytes)
	}

	aggregator.bufferBytes = bufferBytes

	for i := range aggregator.tokenizers {

		aggregator.tokenizers[i] = &utils.Tokenizer{

			Tokens: make([]string, utils.TokenizerLength),
		}
	}

	for i := range aggregator.stringTokens {

		aggregator.stringTokens[i] = map[string]map[int64]struct{}{}

		aggregator.numericTokens[i] = map[int64]map[int64]struct{}{}

		aggregator.passoverStringTokens[i] = map[string]map[int64]struct{}{}

		aggregator.passoverNumericTokens[i] = map[int64]map[int64]struct{}{}
	}

	go func() {

		utils.AggregatorEngineShutdownMutex.Add(1)

		for {

			if aggregator.shutdown || utils.GlobalShutdown {

				break
			}

			aggregator.process()
		}

		utils.AggregatorEngineShutdownMutex.Done()

	}()
}

func (aggregator *EventAggregator) process() {

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			aggregator.logger.Error(fmt.Sprintf("error %v occurred in event aggregator %v", err, aggregator.aggregatorId))

			aggregator.logger.Error(fmt.Sprintf(errorStackTrace, aggregator.aggregatorId, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

			aggregator.logger.Error(fmt.Sprintf("event aggregator %v restarted", aggregator.aggregatorId))

		}
	}()

	for {

		select {

		// request comes from data reader
		case event := <-aggregator.Events:

			err := aggregator.aggregate(event)

			if err != nil {

				aggregator.logger.Error(fmt.Sprintf("error %v occurred while aggregating file %v contexts", err, event.GetStringValue(utils.File)))
			}

		case <-aggregator.ShutdownNotifications:

			for index := range aggregator.valueBuffers {

				if err := unix.Munmap(aggregator.valueBuffers[index]); err != nil {

					aggregator.logger.Warn(fmt.Sprintf("failed to unmap anonymous mapped buffer,reason: %v", err.Error()))
				}
			}

			_ = unix.Munmap(aggregator.txnBufferBytes)

			_ = unix.Munmap(aggregator.bufferBytes)

			aggregator.encoder.MemoryPool.Unmap()

			aggregator.logger.Info(fmt.Sprintf("shutting down event aggregator %v", aggregator.aggregatorId))

			aggregator.shutdown = true

			return

		}
	}
}

/* ------------------------------------- Aggregate -----------------------------------------------------*/

func (aggregator *EventAggregator) aggregate(event utils.MotadataMap) error {

	fileName := event.GetStringValue(utils.File)

	defer func(fileName string) {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			aggregator.logger.Error(fmt.Sprintf("error %v occurred while aggregating events for file %v", err, workingDir+fileName))

			aggregator.logger.Error(fmt.Sprintf(errorStackTrace, aggregator.aggregatorId, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}

		atomic.AddInt32(&aggregator.PendingRequests, -1)

		// gives notification to datareader for completion of file written
		utils.EventAggregationSyncNotifications <- utils.EventAggregationSyncEvent{

			File: fileName, AggregatorId: aggregator.aggregatorId,
		}

		aggregator.cleanup()

		aggregator.decoder.MemoryPool.TestPoolLeak()

	}(fileName)

	// tick - 0 index

	utils.Split(fileName, utils.PathSeparator, aggregator.tokenizers[0])

	utils.Split(aggregator.tokenizers[0].Tokens[aggregator.tokenizers[0].Counts-1], utils.SpecialSeparator, aggregator.tokenizers[0])

	tick := utils.UnixToSeconds(StringToINT64(aggregator.tokenizers[0].Tokens[0]))

	secondTick := utils.SecondsToUnix(tick)

	key := aggregator.tokenizers[0].Tokens[1] + utils.KeySeparator + INT64ToStringValue(utils.GetBaseTickv1(secondTick)) + utils.KeySeparator + aggregator.tokenizers[0].Tokens[3] + utils.KeySeparator + utils.HorizontalFormat

	topNView := strings.Contains(aggregator.tokenizers[0].Tokens[1], datastore.FlowPlugin)

	// if interval is already handled by aggregation then no need to write here.
	if datastore.IsAggregationContextExists(key) {

		for _, interval := range utils.EventAggregationIntervals {

			if utils.ManagerNotifications != nil {

				secondTick = utils.SecondsToUnix(utils.RoundOffSeconds(utils.UnixToSeconds(StringToINT64(aggregator.tokenizers[0].Tokens[0])), interval))

				utils.ManagerNotifications <- utils.MotadataMap{

					utils.Plugin:          aggregator.tokenizers[0].Tokens[1],
					utils.Tick:            secondTick,
					utils.DatastoreFormat: utils.HorizontalFormat,
					utils.OperationType:   utils.Recover,
					utils.Interval:        interval,
				}

				aggregator.logger.Info(fmt.Sprintf("skipping %v key %v tick record as it is being aggregated by the offline job", key, secondTick))
			}

		}

		err := os.Remove(workingDir + fileName)

		if err != nil {

			aggregator.logger.Error(fmt.Sprintf("aggregator %v failed to delete file %v, reason: %v", aggregator.aggregatorId, workingDir+fileName, err))
		}

		return nil

	}

	bufferBytes, err := aggregator.read(workingDir + fileName)

	if err != nil {

		return err
	}

	storeType := utils.DatastoreType(StringToINT(aggregator.tokenizers[0].Tokens[2]))

	aggregator.overflowLength = utils.LogOverflowLength

	switch {

	case storeType == utils.Flow:

		aggregator.overflowLength = utils.OverflowLength

		storeType = utils.FlowAggregation

	case storeType == utils.MetricPolicy || storeType == utils.EventPolicy || storeType == utils.PolicyFlapHistory:

		storeType = utils.PolicyAggregation

	case storeType == utils.Trap:

		storeType = utils.TrapAggregation

	default:
		storeType = utils.LogAggregation

	}

	for _, aggregationInterval := range utils.EventAggregationIntervals {

		aggregator.cleanup()

		err = aggregator.unpack(bufferBytes)

		roundedTick := utils.RoundOffSeconds(tick, aggregationInterval)

		secondsToUnix := utils.SecondsToUnix(roundedTick)

		if err != nil {

			if utils.ManagerNotifications != nil {

				aggregator.logger.Warn(fmt.Sprintf("%v groups probing send to offline job for plugin %v, tick %v and interval %v, reason: %v", aggregator.aggregations.Count(), aggregator.tokenizers[0].Tokens[1], roundedTick, aggregationInterval, err))

				utils.ManagerNotifications <- utils.MotadataMap{

					utils.Plugin:          aggregator.tokenizers[0].Tokens[1],
					utils.Tick:            secondsToUnix,
					utils.DatastoreFormat: utils.HorizontalFormat,
					utils.OperationType:   utils.Recover,
					utils.Interval:        aggregationInterval,
				}
			}

			continue
		}

		key = aggregator.tokenizers[0].Tokens[1] + utils.KeySeparator + INT64ToStringValue(utils.GetBaseTickv1(secondsToUnix)) + utils.KeySeparator + INTToStringValue(aggregationInterval) + utils.KeySeparator + utils.HorizontalFormat

		if datastore.IsAggregationContextPositionExists(key, utils.GetPosition(secondsToUnix, aggregationInterval)) {

			if utils.ManagerNotifications != nil {

				aggregator.logger.Warn(fmt.Sprintf("%v groups merge probing send to offline job for plugin %v, tick %v and interval %v", aggregator.aggregations.Count(), aggregator.tokenizers[0].Tokens[1], roundedTick, aggregationInterval))

				utils.ManagerNotifications <- utils.MotadataMap{

					utils.Plugin:          aggregator.tokenizers[0].Tokens[1],
					utils.Tick:            secondsToUnix,
					utils.DatastoreFormat: utils.HorizontalFormat,
					utils.OperationType:   utils.Recover,
					utils.Interval:        aggregationInterval,
				}
			}

			continue
		}

		aggregator.tick = INT32ToStringValue(roundedTick)

		storeName := (utils.SecondsToDate(roundedTick)) + utils.HyphenSeparator + utils.HorizontalFormat + utils.HyphenSeparator + aggregator.tokenizers[0].Tokens[1] + utils.HyphenSeparator + INTToStringValue(aggregationInterval)

		if store := datastore.GetStore(storeName, storeType, true, true, aggregator.encoder, aggregator.tokenizers[1]); store != nil {

			binary.BigEndian.PutUint64(aggregator.int64ValueBytes, uint64(roundedTick))

			aggregator.setColumnContext(utils.NotAvailable)

			exist := false

			// we merge arrived aggregation to already written aggregation
			err, exist = aggregator.merge(store)

			if err != nil || aggregator.pending {

				if utils.ManagerNotifications != nil {

					aggregator.logger.Warn(fmt.Sprintf("%v groups merge probing send to offline job for plugin %v, tick %v and interval %v", aggregator.aggregations.Count(), aggregator.tokenizers[0].Tokens[1], roundedTick, aggregationInterval))

					utils.ManagerNotifications <- utils.MotadataMap{

						utils.Plugin:          aggregator.tokenizers[0].Tokens[1],
						utils.Tick:            secondsToUnix,
						utils.DatastoreFormat: utils.HorizontalFormat,
						utils.OperationType:   utils.Recover,
						utils.Interval:        aggregationInterval,
					}
				}

				continue
			}

			// means new group pending...
			if aggregator.aggregations.Count() > 0 {

				aggregator.cleanupPool()

				if topNView {

					err = aggregator.top(store, roundedTick, exist)

					if err != nil || aggregator.pending {

						if utils.ManagerNotifications != nil {

							aggregator.logger.Warn(fmt.Sprintf("%v groups top probing send to offline job for plugin %v, tick %v and interval %v", aggregator.aggregations.Count(), aggregator.tokenizers[0].Tokens[1], roundedTick, aggregationInterval))

							utils.ManagerNotifications <- utils.MotadataMap{

								utils.Plugin:          aggregator.tokenizers[0].Tokens[1],
								utils.Tick:            secondsToUnix,
								utils.DatastoreFormat: utils.HorizontalFormat,
								utils.OperationType:   utils.Recover,
								utils.Interval:        aggregationInterval,
							}
						}

						continue
					}

				} else {

					aggregator.setColumnContext(aggregator.valuesByOrdinal.Count())

					// we append new groups
					err = aggregator.append(store, int(store.GetMaxPart(binary.BigEndian.Uint64(aggregator.int64ValueBytes))), aggregator.valuesByOrdinal.Count(), roundedTick, exist)

					if err != nil || aggregator.pending {

						if utils.ManagerNotifications != nil {

							aggregator.logger.Warn(fmt.Sprintf("%v groups append probing send to offline job for plugin %v, tick %v and interval %v", aggregator.aggregations.Count(), aggregator.tokenizers[0].Tokens[1], roundedTick, aggregationInterval))

							utils.ManagerNotifications <- utils.MotadataMap{

								utils.Plugin:          aggregator.tokenizers[0].Tokens[1],
								utils.Tick:            secondsToUnix,
								utils.DatastoreFormat: utils.HorizontalFormat,
								utils.OperationType:   utils.Recover,
								utils.Interval:        aggregationInterval,
							}
						}

						continue
					}
				}

				indexType := getIndexStoreType(storeType)

				// write posting list for appended grouping columns
				for columnIndex := range aggregator.indexableColumns[:aggregator.indexableColumnElementSize] {

					if len(aggregator.stringTokens[columnIndex]) > 0 {

						err = aggregator.writeStringPostingListIndex(roundedTick, aggregationInterval, columnIndex, aggregator.indexableColumns[columnIndex], aggregator.tokenizers[0].Tokens[1], indexType)

						if err != nil {

							aggregator.logger.Error(fmt.Sprintf("failed to update the %v string posting list index, reason :%v", aggregator.indexableColumns[columnIndex], err.Error()))
						}
					}

					if len(aggregator.numericTokens[columnIndex]) > 0 {

						err = aggregator.writeNumericPostingListIndex(roundedTick, aggregationInterval, columnIndex, aggregator.indexableColumns[columnIndex], aggregator.tokenizers[0].Tokens[1], indexType)

						if err != nil {

							aggregator.logger.Error(fmt.Sprintf("failed to update the %v numeric posting list index, reason :%v", aggregator.indexableColumns[columnIndex], err.Error()))
						}
					}
				}
			}
		}

	}

	return nil
}

func (aggregator *EventAggregator) merge(store *storage.Store) (error, bool) {

	exist := false

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			aggregator.logger.Error(fmt.Sprintf("error %v occurred while merging groups for store %v", err, store.GetName()))

			aggregator.logger.Error(fmt.Sprintf(errorStackTrace, aggregator.aggregatorId, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}

		aggregator.ordinals.Clear()

		aggregator.probes.Iter(func(key string, _ struct{}) (stop bool) {

			aggregator.aggregations.Delete(key)

			return stop
		})

		aggregator.probes.Clear()

	}()

	aggregator.pending = true

	parts := int(store.GetMaxPart(binary.BigEndian.Uint64(aggregator.int64ValueBytes)))

	groupPoolIndex, groups := aggregator.encoder.MemoryPool.AcquireStringPool(utils.NotAvailable)

	defer aggregator.encoder.MemoryPool.ReleaseStringPool(groupPoolIndex)

	groupElementSize, ordinalPoolIndex, poolIndex := 0, utils.NotAvailable, utils.NotAvailable

	var dataType DataType

	var ordinals []int32

	keyElementSize := 0

	var valueBuffers [][]byte

	var bufferBytes []byte

	var int64Values []int64

	var errs []error

	var err error

	defer aggregator.cleanupTxn()

	// we iterate over all partitions and merge aggregations where group found
	for partIndex := 0; partIndex <= parts; partIndex++ {

		aggregator.cleanupTxn()

		aggregator.txnPartition = store.GetPartition([]byte(aggregator.tick), aggregator.tokenizers[1])

		part := INTToStringValue(partIndex)

		keyElementSize = 0

		for i := range aggregator.indexableColumns[:aggregator.indexableColumnElementSize] {

			aggregator.keyBuffers[keyElementSize] = []byte(aggregator.tick + utils.KeySeparator + aggregator.indexableColumns[i] + utils.OrdinalSuffix + utils.KeySeparator + part)

			keyElementSize++
		}

		valueBuffers, errs, err = store.GetMultiples(aggregator.keyBuffers[:keyElementSize], aggregator.valueBuffers[:keyElementSize], aggregator.encoder, aggregator.events,
			aggregator.waitGroup, aggregator.tokenizers[1], true)

		if err != nil {

			return err, exist
		}

		// here we iterate and get grouping columns data
		for i := 0; i < keyElementSize; i++ {

			if errs[i] != nil || len(valueBuffers[i]) == 0 {

				break
			}

			exist = true

			ordinalPoolIndex, ordinals, err = aggregator.decoder.DecodeINT32Values(GetEncoding(valueBuffers[i][0]), GetDataType(valueBuffers[i][0]), valueBuffers[i][1:], string(aggregator.keyBuffers[i]), store.GetName(), 0)

			if err != nil {

				return err, exist
			}

			mappingStore := datastore.GetStore(aggregator.indexableColumns[i]+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, aggregator.encoder, aggregator.tokenizers[1])

			if mappingStore == nil {

				return errors.New(fmt.Sprintf(utils.ErrorAcquireStore, aggregator.indexableColumns[i]+utils.HyphenSeparator+datastore.Mappings)), exist
			}

			aggregator.cleanupMappings()

			err, dataType, poolIndex = mappingStore.ResolveMapping(ordinalPoolIndex, aggregator.encoder, aggregator.stringMappings, aggregator.numericMappings, len(ordinals))

			aggregator.encoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)

			if err != nil && !strings.Contains(err.Error(), utils.ErrorIteratorDone) {

				return err, exist
			}

			groupElementSize = len(ordinals)

			if dataType == String {

				if i > 0 {

					for j, value := range aggregator.encoder.MemoryPool.GetStringPool(poolIndex) {

						groups[j] += utils.GroupSeparator + value

					}

				} else {

					for j, value := range aggregator.encoder.MemoryPool.GetStringPool(poolIndex) {

						groups[j] = value
					}
				}

				aggregator.encoder.MemoryPool.ReleaseStringPool(poolIndex)

			} else {

				if i > 0 {

					for j, value := range aggregator.encoder.MemoryPool.GetINT64Pool(poolIndex) {

						groups[j] += utils.GroupSeparator + INT64ToStringValue(value)

					}

				} else {

					for j, value := range aggregator.encoder.MemoryPool.GetINT64Pool(poolIndex) {

						groups[j] = INT64ToStringValue(value)
					}
				}
				aggregator.encoder.MemoryPool.ReleaseINT64Pool(poolIndex)
			}
		}

		aggregator.groups.Clear()

		aggregator.ordinals.Clear()

		// we set position where group are available to merge
		for i := range groups[:groupElementSize] {

			if ordinal, ok := aggregator.ordinalsByValue.Get(groups[i]); ok {

				aggregator.ordinals.Set(uint32(ordinal))

				aggregator.groups.Put(INT32ToStringValue(ordinal), i)
			}
		}

		aggregator.records += groupElementSize

		// if groups are not matched for part then continue
		if aggregator.groups.Count() == 0 {

			aggregator.pending = false

			continue
		}

		ok := false

		position := 0

		// here we put value in temporary memory pool at position where groups are available
		aggregator.aggregations.Iter(func(key string, value int64) (stop bool) {

			utils.Split(key, utils.KeySeparator, aggregator.tokenizers[1])

			if position, ok = aggregator.groups.Get(aggregator.tokenizers[1].Tokens[0]); ok {

				aggregator.probes.Put(key, struct{}{})

				aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[aggregator.columns[aggregator.tokenizers[1].Tokens[1]]+utils.KeySeparator+aggregator.tokenizers[1].Tokens[2]])[position] = value
			}

			return stop
		})

		count := false

		keyElementSize = 0

		for startIndex := 0; startIndex < aggregator.aggregationColumnElementSize; startIndex++ {

			if aggregator.dataTypes[aggregator.aggregationColumns[startIndex]] == String {

				if !count {

					aggregator.keyBuffers[keyElementSize] = []byte(aggregator.tick + utils.KeySeparator + utils.All + utils.KeySeparator + utils.Count + utils.KeySeparator + part)

					keyElementSize++

					count = true
				}
			} else {

				aggregator.keyBuffers[keyElementSize] = []byte(aggregator.tick + utils.KeySeparator + aggregator.aggregationColumns[startIndex] + utils.KeySeparator + utils.Sum + utils.KeySeparator + part)

				keyElementSize++

				if !count {

					aggregator.keyBuffers[keyElementSize] = []byte(aggregator.tick + utils.KeySeparator + utils.All + utils.KeySeparator + utils.Count + utils.KeySeparator + part)

					keyElementSize++

					count = true
				}
			}

			if startIndex == aggregator.aggregationColumnElementSize-1 || keyElementSize == maxKeyElementSize {

				valueBuffers, errs, err = store.GetMultiples(aggregator.keyBuffers[:keyElementSize], aggregator.valueBuffers[:keyElementSize], aggregator.encoder, aggregator.events,
					aggregator.waitGroup, aggregator.tokenizers[1], true)

				if err != nil {

					return err, exist
				}

				decodedPoolIndex, valuePoolIndex, bytePoolIndex := utils.NotAvailable, utils.NotAvailable, utils.NotAvailable

				for i := 0; i < keyElementSize; i++ {

					if errs[i] != nil {

						return errs[i], exist
					}

					if len(valueBuffers[i]) == 0 {

						return errors.New(fmt.Sprintf(utils.ErrorDiskIO, err, aggregator.tokenizers[0].Tokens[1], aggregator.tick, part)), exist
					}

					valuePoolIndex = utils.NotAvailable

					dataType = GetDataType(valueBuffers[i][0])

					switch {

					case dataType == Int8:

						var values []int8

						decodedPoolIndex, values, err = aggregator.decoder.DecodeINT8Values(GetEncoding(valueBuffers[i][0]), valueBuffers[i][1:], string(aggregator.keyBuffers[i]), store.GetName(), 0)

						if err != nil {

							return err, exist
						}

						valuePoolIndex, int64Values = aggregator.encoder.MemoryPool.AcquireINT64Pool(len(values))

						aggregator.encoder.MemoryPool.ResetINT64Pool(valuePoolIndex, len(int64Values), utils.DummyINT64Value)

						INT8ToINT64Values(values, int64Values)

						aggregator.decoder.MemoryPool.ReleaseINT8Pool(decodedPoolIndex)

					case dataType == Int16:

						var values []int16

						decodedPoolIndex, values, err = aggregator.decoder.DecodeINT16Values(GetEncoding(valueBuffers[i][0]), valueBuffers[i][1:], string(aggregator.keyBuffers[i]), store.GetName(), 0)

						if err != nil {

							return err, exist
						}

						valuePoolIndex, int64Values = aggregator.encoder.MemoryPool.AcquireINT64Pool(len(values))

						aggregator.encoder.MemoryPool.ResetINT64Pool(valuePoolIndex, len(int64Values), utils.DummyINT64Value)

						INT16ToINT64Values(values, int64Values)

						aggregator.decoder.MemoryPool.ReleaseINT16Pool(decodedPoolIndex)

					case dataType == Int24 || dataType == Int32:

						var values []int32

						decodedPoolIndex, values, err = aggregator.decoder.DecodeINT32Values(GetEncoding(valueBuffers[i][0]), dataType, valueBuffers[i][1:], string(aggregator.keyBuffers[i]), store.GetName(), 0)

						if err != nil {

							return err, exist
						}

						valuePoolIndex, int64Values = aggregator.encoder.MemoryPool.AcquireINT64Pool(len(values))

						aggregator.encoder.MemoryPool.ResetINT64Pool(valuePoolIndex, len(int64Values), utils.DummyINT64Value)

						INT32ToINT64Values(values, int64Values)

						aggregator.decoder.MemoryPool.ReleaseINT32Pool(decodedPoolIndex)

					case dataType == Int40 || dataType == Int48 || dataType == Int56 || dataType == Int64:

						valuePoolIndex, int64Values, err = aggregator.decoder.DecodeINT64Values(GetEncoding(valueBuffers[i][0]), dataType, valueBuffers[i][1:], string(aggregator.keyBuffers[i]), store.GetName(), 0)

						if err != nil {

							return err, exist
						}
					}

					utils.Split(string(aggregator.keyBuffers[i]), utils.KeySeparator, aggregator.tokenizers[1])

					key := aggregator.tokenizers[1].Tokens[1] + utils.KeySeparator + aggregator.tokenizers[1].Tokens[2]

					// we add new aggregated value with old aggregated value
					aggregator.groups.Iter(func(_ string, position int) (stop bool) {

						// overflow value
						if int64Values[position] == math.MaxInt64 {

							return stop
						}

						// check for overflow value and if sum overflows than keep max int64 value

						updatedValue := int64Values[position] + aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[key])[position]

						if aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[key])[position] < 0 || updatedValue >= 0 {

							int64Values[position] = updatedValue
						} else {

							int64Values[position] = math.MaxInt64
						}

						return stop
					})

					dataType = GetMaxDataTypeINT64Values(int64Values)

					switch {

					case dataType == Int8:

						tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireINT8Pool(len(int64Values))

						aggregator.encoder.MemoryPool.ResetINT8Pool(tempPoolIndex, len(tempValues), 0)

						INT64ToINT8Values(int64Values, tempValues)

						bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeINT8Values(None, tempValues, utils.MaxValueBytes)

						aggregator.encoder.MemoryPool.ReleaseINT8Pool(tempPoolIndex)

					case dataType == Int16:

						tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireINT16Pool(len(int64Values))

						aggregator.encoder.MemoryPool.ResetINT16Pool(tempPoolIndex, len(tempValues), 0)

						INT64ToINT16Values(int64Values, tempValues)

						bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeINT16Values(None, tempValues, utils.MaxValueBytes)

						aggregator.encoder.MemoryPool.ReleaseINT16Pool(tempPoolIndex)

					case dataType == Int24 || dataType == Int32:

						tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireINT32Pool(len(int64Values))

						aggregator.encoder.MemoryPool.ResetINT32Pool(tempPoolIndex, len(tempValues), 0)

						INT64ToINT32Values(int64Values, tempValues)

						bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeINT32Values(tempValues, None, dataType, GetDataTypeBits(dataType), utils.MaxValueBytes)

						aggregator.encoder.MemoryPool.ReleaseINT32Pool(tempPoolIndex)

					case dataType == Int40 || dataType == Int48 || dataType == Int56 || dataType == Int64:

						bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeINT64Values(int64Values, None, dataType, utils.MaxValueBytes)

					}

					if err != nil {

						aggregator.encoder.MemoryPool.ReleaseINT64Pool(valuePoolIndex)

						return err, exist
					}

					err = aggregator.writeTxn(aggregator.keyBuffers[i], bufferBytes)

					aggregator.encoder.MemoryPool.ReleaseBytePool(bytePoolIndex)

					aggregator.encoder.MemoryPool.ReleaseINT64Pool(valuePoolIndex)

					if err != nil {

						return err, exist
					}
				}

				keyElementSize = 0
			}
		}

		err = aggregator.commit(store)

		if err != nil {

			return err, exist
		}

		aggregator.cleanupTxn()

		// remove written aggregation group from ordinals
		aggregator.ordinals.Range(func(ordinal uint32) {

			aggregator.valuesByOrdinal.Delete(int32(ordinal))
		})
	}

	aggregator.pending = false

	return nil, exist
}

func (aggregator *EventAggregator) append(store *storage.Store, existingParts, groups int, tick int32, exist bool) error {

	aggregator.pending = true

	aggregator.cleanupTxn()

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			aggregator.logger.Error(fmt.Sprintf("error %v occurred while appending new groups for store %v", err, store.GetName()))

			aggregator.logger.Error(fmt.Sprintf(errorStackTrace, aggregator.aggregatorId, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}

		aggregator.cleanupTxn()

	}()

	aggregator.groups.Clear()

	ok := false

	group, column := utils.Empty, utils.Empty

	position := 0

	// here we iterate over aggregated columns and we align then linearly
	aggregator.aggregations.Iter(func(key string, value int64) (stop bool) {

		utils.Split(key, utils.KeySeparator, aggregator.tokenizers[1])

		column = aggregator.columns[aggregator.tokenizers[1].Tokens[1]] + utils.KeySeparator + aggregator.tokenizers[1].Tokens[2]

		if position, ok = aggregator.groups.Get(aggregator.tokenizers[1].Tokens[0]); !ok {

			position = aggregator.groups.Count()

			aggregator.groups.Put(aggregator.tokenizers[1].Tokens[0], position)

			group, _ = aggregator.valuesByOrdinal.Get(utils.StringToInt32(aggregator.tokenizers[1].Tokens[0]))

			utils.Split(group, utils.GroupSeparator, aggregator.tokenizers[1])

			for i := 0; i < aggregator.tokenizers[1].Counts; i++ {

				if aggregator.dataTypes[aggregator.indexableColumns[i]] == String {

					aggregator.encoder.MemoryPool.GetStringPool(aggregator.poolIndices[aggregator.indexableColumns[i]])[position] = aggregator.tokenizers[1].Tokens[i]
				} else {

					// convert numeric datatype to string
					if aggregator.tokenizers[1].Tokens[i] == utils.Empty {

						tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireStringPool(utils.NotAvailable)

						INT64ToStringValues(aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[aggregator.indexableColumns[i]])[:groups], tempValues)

						aggregator.encoder.MemoryPool.ReleaseINT64Pool(aggregator.poolIndices[aggregator.indexableColumns[i]])

						aggregator.poolIndices[aggregator.indexableColumns[i]] = tempPoolIndex

						aggregator.encoder.MemoryPool.GetStringPool(aggregator.poolIndices[aggregator.indexableColumns[i]])[position] = aggregator.tokenizers[1].Tokens[i]

						aggregator.dataTypes[aggregator.indexableColumns[i]] = String

						continue
					}
					aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[aggregator.indexableColumns[i]])[position] = StringToINT64(aggregator.tokenizers[1].Tokens[i])
				}
			}
		}

		aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[column])[position] = value

		return stop
	})

	var mappingStore *storage.Store

	var err error

	var bufferBytes []byte

	var errs []error

	start, end, ordinalPoolIndex, bytePoolIndex, decodedPoolIndex, startPosition := 0, 0, utils.NotAvailable, utils.NotAvailable, utils.NotAvailable, 0

	currentPart := existingParts

	// if data exist then we get data and calculate start position and get ordinals
	if exist {

		aggregator.keyBuffers[0] = []byte(aggregator.tick + utils.KeySeparator + aggregator.indexableColumns[0] + utils.OrdinalSuffix + utils.KeySeparator + INTToStringValue(existingParts))

		var valueBufferBytes []byte

		found := false

		found, valueBufferBytes, err = store.Get(aggregator.keyBuffers[0], aggregator.valueBuffers[0], aggregator.encoder, aggregator.event, aggregator.waitGroup, aggregator.tokenizers[1], true)

		if err != nil {

			return err
		}

		if found {

			var ordinals []int32

			ordinalPoolIndex, ordinals, err = aggregator.decoder.DecodeINT32Values(GetEncoding(valueBufferBytes[0]), GetDataType(valueBufferBytes[0]), valueBufferBytes[1:], string(aggregator.keyBuffers[0]), store.GetName(), 0)

			if err != nil {

				return err
			}

			startPosition = len(ordinals)

			if startPosition == aggregator.overflowLength {

				startPosition = 0

				exist = false

				existingParts++
			}

			aggregator.decoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)

			ordinalPoolIndex = utils.NotAvailable
		} else {

			exist = false
		}
	}

	groupings := aggregator.groups.Count()

	for k := 0; k < groupings; {

		aggregator.cleanupTxn()

		aggregator.txnPartition = store.GetPartition([]byte(aggregator.tick), aggregator.tokenizers[1])

		start = k

		end = (start + aggregator.overflowLength) - startPosition

		if end > groupings {

			end = groupings
		}

		// logic

		part := INTToStringValue(existingParts)

		eventId := utils.GetAggregationEventId(tick, uint16(existingParts))

		keyElementSize := 0

		var valueBuffers [][]byte

		for i := range aggregator.indexableColumns[:aggregator.indexableColumnElementSize] {

			aggregator.keyBuffers[i] = []byte(aggregator.tick + utils.KeySeparator + aggregator.indexableColumns[i] + utils.OrdinalSuffix + utils.KeySeparator + part)
		}

		if exist {

			valueBuffers, errs, err = store.GetMultiples(aggregator.keyBuffers[:aggregator.indexableColumnElementSize], aggregator.valueBuffers[:aggregator.indexableColumnElementSize], aggregator.encoder, aggregator.events, aggregator.waitGroup, aggregator.tokenizers[1], true)

			if err != nil {

				return err
			}
		}

		// here we append indexable columns
		for i := 0; i < aggregator.indexableColumnElementSize; i++ {

			mappingStore = datastore.GetStore(aggregator.indexableColumns[i]+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, aggregator.encoder, aggregator.tokenizers[1])

			if mappingStore == nil {

				return errors.New(fmt.Sprintf(utils.ErrorAcquireStore, aggregator.indexableColumns[i]))
			}

			if startPosition > 0 {

				if errs[i] != nil {

					return errs[i]
				}

				if len(valueBuffers[i]) == 0 {

					return errors.New(fmt.Sprintf(utils.ErrorDiskIO, err, aggregator.tokenizers[0].Tokens[1], aggregator.tick, existingParts))
				}

				decodedPoolIndex, _, err = aggregator.decoder.DecodeINT32Values(GetEncoding(valueBuffers[i][0]), GetDataType(valueBuffers[i][0]),
					valueBuffers[i][1:], string(aggregator.keyBuffers[i]), store.GetName(), 0)

				if err != nil {

					return err
				}
			}

			if aggregator.dataTypes[aggregator.indexableColumns[i]] == String {

				tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireStringPool(end - start)

				copy(tempValues, aggregator.encoder.MemoryPool.GetStringPool(aggregator.poolIndices[aggregator.indexableColumns[i]])[start:end])

				err, ordinalPoolIndex, _ = mappingStore.MapStringValues(tempPoolIndex, aggregator.encoder, end-start)

				aggregator.encoder.MemoryPool.ReleaseStringPool(tempPoolIndex)

				if err != nil {

					return err
				}

				// we iterate over grouping columns value and store it to write posting list
				for _, value := range aggregator.encoder.MemoryPool.GetStringPool(aggregator.poolIndices[aggregator.indexableColumns[i]])[start:end] {

					if _, ok = aggregator.stringTokens[i][value]; !ok {

						aggregator.stringTokens[i][value] = map[int64]struct{}{}
					}

					aggregator.stringTokens[i][value][eventId] = struct{}{}
				}

			} else {

				tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireINT64Pool(end - start)

				aggregator.encoder.MemoryPool.ResetINT64Pool(tempPoolIndex, len(tempValues), 0)

				copy(tempValues, aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[aggregator.indexableColumns[i]])[start:end])

				err, ordinalPoolIndex, _ = mappingStore.MapNumericValues(tempPoolIndex, aggregator.encoder, end-start)

				aggregator.encoder.MemoryPool.ReleaseINT64Pool(tempPoolIndex)

				if err != nil {

					return err
				}

				// we iterate over grouping columns value and store it to write posting list
				for _, value := range aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[aggregator.indexableColumns[i]])[start:end] {

					if _, ok = aggregator.numericTokens[i][value]; !ok {

						aggregator.numericTokens[i][value] = map[int64]struct{}{}
					}

					aggregator.numericTokens[i][value][eventId] = struct{}{}
				}
			}

			tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireINT32Pool(startPosition + (end - start))

			aggregator.encoder.MemoryPool.ResetINT32Pool(tempPoolIndex, len(tempValues), 0)

			if startPosition > 0 {

				copy(tempValues, aggregator.encoder.MemoryPool.GetINT32Pool(decodedPoolIndex)[:startPosition])

				aggregator.encoder.MemoryPool.ReleaseINT32Pool(decodedPoolIndex)
			}

			copy(tempValues[startPosition:], aggregator.encoder.MemoryPool.GetINT32Pool(ordinalPoolIndex))

			bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeINT32Values(tempValues, None, Int32, GetDataTypeBits(Int32), utils.MaxValueBytes)

			aggregator.encoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)

			aggregator.encoder.MemoryPool.ReleaseINT32Pool(tempPoolIndex)

			if err != nil {

				return err
			}

			err = aggregator.writeTxn(aggregator.keyBuffers[i], bufferBytes)

			aggregator.encoder.MemoryPool.ReleaseBytePool(bytePoolIndex)

			if err != nil {

				return err
			}
		}

		count := false

		//  here we add aggregated columns
		for startIndex := 0; startIndex < aggregator.aggregationColumnElementSize; startIndex++ {

			if aggregator.dataTypes[aggregator.aggregationColumns[startIndex]] == Int64 {

				aggregator.buffers[keyElementSize] = []byte(aggregator.aggregationColumns[startIndex] + utils.KeySeparator + utils.Sum)

				aggregator.keyBuffers[keyElementSize] = []byte(aggregator.tick + utils.KeySeparator + aggregator.aggregationColumns[startIndex] + utils.KeySeparator + utils.Sum + utils.KeySeparator + part)

				keyElementSize++

				if !count {

					aggregator.buffers[keyElementSize] = []byte(utils.All + utils.KeySeparator + utils.Count)

					aggregator.keyBuffers[keyElementSize] = []byte(aggregator.tick + utils.KeySeparator + utils.All + utils.KeySeparator + utils.Count + utils.KeySeparator + part)

					keyElementSize++

					count = true
				}

			} else if !count {

				aggregator.buffers[keyElementSize] = []byte(utils.All + utils.KeySeparator + utils.Count)

				aggregator.keyBuffers[keyElementSize] = []byte(aggregator.tick + utils.KeySeparator + utils.All + utils.KeySeparator + utils.Count + utils.KeySeparator + part)

				keyElementSize++

				count = true
			}

			if startIndex == aggregator.aggregationColumnElementSize-1 || keyElementSize == maxKeyElementSize {

				if exist {

					valueBuffers, errs, err = store.GetMultiples(aggregator.keyBuffers[:keyElementSize], aggregator.valueBuffers[:keyElementSize], aggregator.encoder, aggregator.events, aggregator.waitGroup, aggregator.tokenizers[1], true)

					if err != nil {

						return err
					}
				}

				for i := 0; i < keyElementSize; i++ {

					poolIndex := utils.NotAvailable

					if startPosition > 0 { // means old values still available in for this part

						if errs[i] != nil {

							return errs[i]
						}

						if len(valueBuffers[i]) == 0 {

							return errors.New(fmt.Sprintf(utils.ErrorDiskIO, err, aggregator.tokenizers[0].Tokens[1], aggregator.tick, existingParts))
						}

						existingDataType := GetDataType(valueBuffers[i][0])

						switch {

						case existingDataType == Int8:

							decodedPoolIndex, _, err = aggregator.decoder.DecodeINT8Values(GetEncoding(valueBuffers[i][0]), valueBuffers[i][1:],
								string(aggregator.keyBuffers[i]), store.GetName(), 0)

							if err != nil {

								return err
							}

							poolIndex, _ = aggregator.encoder.MemoryPool.AcquireINT64Pool(startPosition + (end - start))

							aggregator.encoder.MemoryPool.ResetINT64Pool(poolIndex, startPosition+(end-start), 0)

							INT8ToINT64Values(aggregator.encoder.MemoryPool.GetINT8Pool(decodedPoolIndex)[:startPosition], aggregator.encoder.MemoryPool.GetINT64Pool(poolIndex)[:startPosition])

							aggregator.encoder.MemoryPool.ReleaseINT8Pool(decodedPoolIndex)

							copy(aggregator.encoder.MemoryPool.GetINT64Pool(poolIndex)[startPosition:], aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[string(aggregator.buffers[i])])[start:end])

						case existingDataType == Int16:

							decodedPoolIndex, _, err = aggregator.decoder.DecodeINT16Values(GetEncoding(valueBuffers[i][0]), valueBuffers[i][1:],
								string(aggregator.keyBuffers[i]), store.GetName(), 0)

							if err != nil {

								return err
							}

							poolIndex, _ = aggregator.encoder.MemoryPool.AcquireINT64Pool(startPosition + (end - start))

							aggregator.encoder.MemoryPool.ResetINT64Pool(poolIndex, startPosition+(end-start), 0)

							INT16ToINT64Values(aggregator.encoder.MemoryPool.GetINT16Pool(decodedPoolIndex)[:startPosition], aggregator.encoder.MemoryPool.GetINT64Pool(poolIndex)[:startPosition])

							aggregator.encoder.MemoryPool.ReleaseINT16Pool(decodedPoolIndex)

							copy(aggregator.encoder.MemoryPool.GetINT64Pool(poolIndex)[startPosition:], aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[string(aggregator.buffers[i])])[start:end])

						case existingDataType == Int24 || existingDataType == Int32:

							decodedPoolIndex, _, err = aggregator.decoder.DecodeINT32Values(GetEncoding(valueBuffers[i][0]), GetDataType(valueBuffers[i][0]), valueBuffers[i][1:],
								string(aggregator.keyBuffers[i]), store.GetName(), 0)

							if err != nil {

								return err
							}

							poolIndex, _ = aggregator.encoder.MemoryPool.AcquireINT64Pool(startPosition + (end - start))

							aggregator.encoder.MemoryPool.ResetINT64Pool(poolIndex, startPosition+(end-start), 0)

							INT32ToINT64Values(aggregator.encoder.MemoryPool.GetINT32Pool(decodedPoolIndex)[:startPosition], aggregator.encoder.MemoryPool.GetINT64Pool(poolIndex)[:startPosition])

							aggregator.encoder.MemoryPool.ReleaseINT32Pool(decodedPoolIndex)

							copy(aggregator.encoder.MemoryPool.GetINT64Pool(poolIndex)[startPosition:], aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[string(aggregator.buffers[i])])[start:end])

						case existingDataType == Int40 || existingDataType == Int48 || existingDataType == Int56 || existingDataType == Int64:

							decodedPoolIndex, _, err = aggregator.decoder.DecodeINT64Values(GetEncoding(valueBuffers[i][0]), GetDataType(valueBuffers[i][0]), valueBuffers[i][1:],
								string(aggregator.keyBuffers[i]), store.GetName(), 0)

							if err != nil {

								return err
							}

							poolIndex, _ = aggregator.encoder.MemoryPool.AcquireINT64Pool(startPosition + (end - start))

							aggregator.encoder.MemoryPool.ResetINT64Pool(poolIndex, startPosition+(end-start), 0)

							copy(aggregator.encoder.MemoryPool.GetINT64Pool(poolIndex), aggregator.encoder.MemoryPool.GetINT64Pool(decodedPoolIndex)[:startPosition])

							aggregator.encoder.MemoryPool.ReleaseINT64Pool(decodedPoolIndex)

							copy(aggregator.encoder.MemoryPool.GetINT64Pool(poolIndex)[startPosition:], aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[string(aggregator.buffers[i])])[start:end])
						}
					} else {

						poolIndex, _ = aggregator.encoder.MemoryPool.AcquireINT64Pool(end - start)

						aggregator.encoder.MemoryPool.ResetINT64Pool(poolIndex, end-start, 0)

						copy(aggregator.encoder.MemoryPool.GetINT64Pool(poolIndex), aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[string(aggregator.buffers[i])])[start:end])
					}

					dataType := GetMaxDataTypeINT64Values(aggregator.encoder.MemoryPool.GetINT64Pool(poolIndex))

					switch {

					case dataType == Int8:

						tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireINT8Pool(startPosition + (end - start))

						aggregator.encoder.MemoryPool.ResetINT8Pool(tempPoolIndex, len(tempValues), 0)

						INT64ToINT8Values(aggregator.encoder.MemoryPool.GetINT64Pool(poolIndex), tempValues)

						bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeINT8Values(None, tempValues, utils.MaxValueBytes)

						aggregator.encoder.MemoryPool.ReleaseINT8Pool(tempPoolIndex)

					case dataType == Int16:

						tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireINT16Pool(startPosition + (end - start))

						aggregator.encoder.MemoryPool.ResetINT16Pool(tempPoolIndex, len(tempValues), 0)

						INT64ToINT16Values(aggregator.encoder.MemoryPool.GetINT64Pool(poolIndex), tempValues)

						bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeINT16Values(None, tempValues, utils.MaxValueBytes)

						aggregator.encoder.MemoryPool.ReleaseINT16Pool(tempPoolIndex)

					case dataType == Int24 || dataType == Int32:

						tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireINT32Pool(startPosition + (end - start))

						aggregator.encoder.MemoryPool.ResetINT32Pool(tempPoolIndex, len(tempValues), 0)

						INT64ToINT32Values(aggregator.encoder.MemoryPool.GetINT64Pool(poolIndex), tempValues)

						bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeINT32Values(tempValues, None, dataType, GetDataTypeBits(dataType), utils.MaxValueBytes)

						aggregator.encoder.MemoryPool.ReleaseINT32Pool(tempPoolIndex)

					case dataType == Int40 || dataType == Int48 || dataType == Int56 || dataType == Int64:

						bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeINT64Values(aggregator.encoder.MemoryPool.GetINT64Pool(poolIndex), None, dataType, utils.MaxValueBytes)
					}

					aggregator.encoder.MemoryPool.ReleaseINT64Pool(poolIndex)

					if err != nil {

						return err
					}

					err = aggregator.writeTxn(aggregator.keyBuffers[i], bufferBytes)

					aggregator.encoder.MemoryPool.ReleaseBytePool(bytePoolIndex)

					if err != nil {

						return err
					}
				}

				keyElementSize = 0
			}
		}

		k += end - start

		exist = false

		startPosition = 0

		err = aggregator.commit(store)

		if err != nil {

			return err
		}

		// if part is extended then updated in store as well
		if currentPart != existingParts {

			err = store.AddMultipartKey(binary.BigEndian.Uint64(aggregator.int64ValueBytes), uint16(1))

			if err != nil {

				return err
			}
		}

		existingParts++
	}

	aggregator.pending = false

	return nil
}

func (aggregator *EventAggregator) top(store *storage.Store, tick int32, exist bool) error {

	aggregator.cleanupTxn()

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			aggregator.logger.Error(fmt.Sprintf("error %v occurred while calculating top groups for %v", err, store.GetName()))

			aggregator.logger.Error(fmt.Sprintf("!!!STACK TRACE for event aggregator %v!!! \n %v", aggregator.aggregatorId, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}

		aggregator.cleanupTxn()

		aggregator.groups.Clear()

		aggregator.ordinals.Clear()
	}()

	aggregator.pending = true

	records := aggregator.records + aggregator.valuesByOrdinal.Count()

	aggregator.setColumnContext(records)

	keyElementSize := 0

	var valueBuffers [][]byte

	var errs []error

	var err error

	parts := int(store.GetMaxPart(binary.BigEndian.Uint64(aggregator.int64ValueBytes)))

	ordinalPoolIndex, mappingPoolIndex := utils.NotAvailable, utils.NotAvailable

	var dataType DataType

	var ordinals []int32

	startPosition, endPosition := 0, 0

	aggregator.txnPartition = store.GetPartition([]byte(aggregator.tick), aggregator.tokenizers[1])

	for partIndex := 0; partIndex <= parts; partIndex++ {

		part := INTToStringValue(partIndex)

		keyElementSize = 0

		for i := range aggregator.indexableColumns[:aggregator.indexableColumnElementSize] {

			aggregator.keyBuffers[keyElementSize] = []byte(aggregator.tick + utils.KeySeparator + aggregator.indexableColumns[i] + utils.OrdinalSuffix + utils.KeySeparator + part)

			keyElementSize++
		}

		valueBuffers, errs, err = store.GetMultiples(aggregator.keyBuffers[:keyElementSize], aggregator.valueBuffers[:keyElementSize], aggregator.encoder, aggregator.events, aggregator.waitGroup, aggregator.tokenizers[1], true)

		if err != nil {

			return err
		}

		for i := 0; i < keyElementSize; i++ {

			if errs[i] != nil {

				if aggregator.records == 0 {

					break
				}

				return errs[i]
			}

			if len(valueBuffers[i]) == 0 {

				if aggregator.records == 0 {

					break
				}

				return errors.New(fmt.Sprintf(utils.ErrorDiskIO, err, aggregator.tokenizers[0].Tokens[1], aggregator.tick, part))
			}

			ordinalPoolIndex, ordinals, err = aggregator.decoder.DecodeINT32Values(GetEncoding(valueBuffers[i][0]), GetDataType(valueBuffers[i][0]), valueBuffers[i][1:], string(aggregator.keyBuffers[i]), store.GetName(), 0)

			if err != nil {

				return err
			}

			mappingStore := datastore.GetStore(aggregator.indexableColumns[i]+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, aggregator.encoder, aggregator.tokenizers[1])

			if mappingStore == nil {

				aggregator.encoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)

				return errors.New(fmt.Sprintf(utils.ErrorAcquireStore, aggregator.indexableColumns[i]+utils.HyphenSeparator+datastore.Mappings))
			}

			err, dataType, mappingPoolIndex = mappingStore.ResolveMapping(ordinalPoolIndex, aggregator.encoder, aggregator.stringMappings, aggregator.numericMappings, len(ordinals))

			if err != nil && !strings.Contains(err.Error(), utils.ErrorIteratorDone) {

				aggregator.encoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)

				return err
			}

			if i == 0 {

				endPosition = startPosition + len(ordinals)
			}

			aggregator.cleanupMappings()

			if dataType != aggregator.dataTypes[aggregator.indexableColumns[i]] {

				aggregator.encoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)

				return errors.New("invalid data type")
			}

			if dataType == String {

				copy(aggregator.encoder.MemoryPool.GetStringPool(aggregator.poolIndices[aggregator.indexableColumns[i]])[startPosition:endPosition], aggregator.encoder.MemoryPool.GetStringPool(mappingPoolIndex))

				aggregator.encoder.MemoryPool.ReleaseStringPool(mappingPoolIndex)
			} else {

				copy(aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[aggregator.indexableColumns[i]])[startPosition:endPosition], aggregator.encoder.MemoryPool.GetINT64Pool(mappingPoolIndex))

				aggregator.encoder.MemoryPool.ReleaseINT64Pool(mappingPoolIndex)
			}

			aggregator.encoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)
		}

		keyElementSize = 0

		count := false

		for startIndex := 0; startIndex < aggregator.aggregationColumnElementSize; startIndex++ {

			if aggregator.dataTypes[aggregator.aggregationColumns[startIndex]] == Int64 {

				aggregator.buffers[keyElementSize] = []byte(aggregator.aggregationColumns[startIndex] + utils.KeySeparator + utils.Sum)

				aggregator.keyBuffers[keyElementSize] = []byte(aggregator.tick + utils.KeySeparator + aggregator.aggregationColumns[startIndex] + utils.KeySeparator + utils.Sum + utils.KeySeparator + part)

				keyElementSize++

				if !count {

					aggregator.buffers[keyElementSize] = []byte(utils.All + utils.KeySeparator + utils.Count)

					aggregator.keyBuffers[keyElementSize] = []byte(aggregator.tick + utils.KeySeparator + utils.All + utils.KeySeparator + utils.Count + utils.KeySeparator + part)

					keyElementSize++

					count = true
				}

			} else if !count {

				aggregator.buffers[keyElementSize] = []byte(utils.All + utils.KeySeparator + utils.Count)

				aggregator.keyBuffers[keyElementSize] = []byte(aggregator.tick + utils.KeySeparator + utils.All + utils.KeySeparator + utils.Count + utils.KeySeparator + part)

				keyElementSize++

				count = true
			}

			if startIndex == aggregator.aggregationColumnElementSize-1 || keyElementSize == maxKeyElementSize {

				valueBuffers, errs, err = store.GetMultiples(aggregator.keyBuffers[:keyElementSize], aggregator.valueBuffers[:keyElementSize], aggregator.encoder, aggregator.events, aggregator.waitGroup, aggregator.tokenizers[1], true)

				if err != nil {

					return err
				}

				for i := 0; i < keyElementSize; i++ {

					poolIndex, decodedPoolIndex := utils.NotAvailable, utils.NotAvailable

					if errs[i] != nil {

						if aggregator.records == 0 {

							break
						}

						return errs[i]
					}

					if len(valueBuffers[i]) == 0 {

						if aggregator.records == 0 {

							break
						}

						return errors.New(fmt.Sprintf(utils.ErrorDiskIO, err, aggregator.tokenizers[0].Tokens[1], aggregator.tick, part))
					}

					existingDataType := GetDataType(valueBuffers[i][0])

					switch {

					case existingDataType == Int8:

						var decodedValues []int8

						decodedPoolIndex, decodedValues, err = aggregator.decoder.DecodeINT8Values(GetEncoding(valueBuffers[i][0]), valueBuffers[i][1:],
							string(aggregator.keyBuffers[i]), store.GetName(), 0)

						if err != nil {

							return err
						}

						poolIndex, _ = aggregator.encoder.MemoryPool.AcquireINT64Pool(len(decodedValues))

						aggregator.encoder.MemoryPool.ResetINT64Pool(poolIndex, len(decodedValues), 0)

						INT8ToINT64Values(decodedValues, aggregator.encoder.MemoryPool.GetINT64Pool(poolIndex))

						aggregator.encoder.MemoryPool.ReleaseINT8Pool(decodedPoolIndex)

					case existingDataType == Int16:

						var decodedValues []int16

						decodedPoolIndex, decodedValues, err = aggregator.decoder.DecodeINT16Values(GetEncoding(valueBuffers[i][0]), valueBuffers[i][1:],
							string(aggregator.keyBuffers[i]), store.GetName(), 0)

						if err != nil {

							return err
						}

						poolIndex, _ = aggregator.encoder.MemoryPool.AcquireINT64Pool(len(decodedValues))

						aggregator.encoder.MemoryPool.ResetINT64Pool(poolIndex, len(decodedValues), 0)

						INT16ToINT64Values(decodedValues, aggregator.encoder.MemoryPool.GetINT64Pool(poolIndex))

						aggregator.encoder.MemoryPool.ReleaseINT16Pool(decodedPoolIndex)

					case existingDataType == Int24 || existingDataType == Int32:

						var decodedValues []int32

						decodedPoolIndex, decodedValues, err = aggregator.decoder.DecodeINT32Values(GetEncoding(valueBuffers[i][0]), GetDataType(valueBuffers[i][0]), valueBuffers[i][1:],
							string(aggregator.keyBuffers[i]), store.GetName(), 0)

						if err != nil {

							return err
						}

						poolIndex, _ = aggregator.encoder.MemoryPool.AcquireINT64Pool(len(decodedValues))

						aggregator.encoder.MemoryPool.ResetINT64Pool(poolIndex, len(decodedValues), 0)

						INT32ToINT64Values(decodedValues, aggregator.encoder.MemoryPool.GetINT64Pool(poolIndex))

						aggregator.encoder.MemoryPool.ReleaseINT32Pool(decodedPoolIndex)

					case existingDataType == Int40 || existingDataType == Int48 || existingDataType == Int56 || existingDataType == Int64:

						poolIndex, _, err = aggregator.decoder.DecodeINT64Values(GetEncoding(valueBuffers[i][0]), GetDataType(valueBuffers[i][0]), valueBuffers[i][1:],
							string(aggregator.keyBuffers[i]), store.GetName(), 0)

						if err != nil {

							return err
						}
					}

					copy(aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[string(aggregator.buffers[i])])[startPosition:endPosition], aggregator.encoder.MemoryPool.GetINT64Pool(poolIndex))

					aggregator.encoder.MemoryPool.ReleaseINT64Pool(poolIndex)
				}

				keyElementSize = 0
			}
		}

		startPosition = endPosition
	}

	aggregator.groups.Clear()

	position := endPosition

	keyElementSize = 0

	for indices := range aggregator.poolIndices {

		if strings.Contains(indices, utils.KeySeparator) {

			keyElementSize++
		}
	}

	aggregator.aggregations.Iter(func(key string, value int64) (stop bool) {

		utils.Split(key, utils.KeySeparator, aggregator.tokenizers[1])

		offset, found := aggregator.groups.Get(aggregator.tokenizers[1].Tokens[0])

		if !found {

			aggregator.groups.Put(aggregator.tokenizers[1].Tokens[0], position)

			group, _ := aggregator.valuesByOrdinal.Get(StringToINT32(aggregator.tokenizers[1].Tokens[0]))

			utils.Split(group, utils.GroupSeparator, aggregator.tokenizers[2])

			for i := 0; i < aggregator.tokenizers[2].Counts; i++ {

				if aggregator.dataTypes[aggregator.indexableColumns[i]] == String {

					aggregator.encoder.MemoryPool.GetStringPool(aggregator.poolIndices[aggregator.indexableColumns[i]])[position] = aggregator.tokenizers[2].Tokens[i]
				} else {

					// convert numeric datatype to string
					if aggregator.tokenizers[2].Tokens[i] == utils.Empty {

						tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireStringPool(records)

						INT64ToStringValues(aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[aggregator.indexableColumns[i]]), tempValues)

						aggregator.encoder.MemoryPool.ReleaseINT64Pool(aggregator.poolIndices[aggregator.indexableColumns[i]])

						aggregator.poolIndices[aggregator.indexableColumns[i]] = tempPoolIndex

						aggregator.encoder.MemoryPool.GetStringPool(aggregator.poolIndices[aggregator.indexableColumns[i]])[position] = aggregator.tokenizers[2].Tokens[i]

						aggregator.dataTypes[aggregator.indexableColumns[i]] = String

					} else {

						aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[aggregator.indexableColumns[i]])[position] = StringToINT64(aggregator.tokenizers[2].Tokens[i])
					}
				}
			}

			offset = position

			position++
		}

		aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[aggregator.columns[aggregator.tokenizers[1].Tokens[1]]+utils.KeySeparator+aggregator.tokenizers[1].Tokens[2]])[offset] = value

		return stop
	})

	groupPoolIndex, groups := aggregator.encoder.MemoryPool.AcquireINT32Pool(records)

	aggregator.encoder.MemoryPool.ResetINT32Pool(groupPoolIndex, records, 0)

	defer aggregator.encoder.MemoryPool.ReleaseINT32Pool(groupPoolIndex)

	for i := range groups {

		groups[i] = int32(i)
	}

	valuePoolIndex, values := aggregator.encoder.MemoryPool.AcquireINT64Pool(records)

	defer aggregator.encoder.MemoryPool.ReleaseINT64Pool(valuePoolIndex)

	copy(values, aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[datastore.FlowTopColumn]))

	sort.Sort(INT32Column{groups: groups, values: values})

	if utils.DebugEnabled() {

		aggregator.logger.Debug(fmt.Sprintf("aggregator %v applied top grouping limit for store %v of %v from total records of %v", aggregator.aggregatorId, store.GetName(), utils.MaxFlowTopNInsertionGroups, records))
	}

	if records < utils.MaxFlowTopNInsertionGroups {

		for i := range groups[:records] {

			aggregator.ordinals.Set(uint32(groups[i]))
		}

	} else {

		for i := 0; i < utils.MaxFlowTopNInsertionGroups/2; i++ {

			aggregator.ordinals.Set(uint32(groups[i]))
		}

		for i := records - (utils.MaxFlowTopNInsertionGroups / 2); i < records; i++ {

			aggregator.ordinals.Set(uint32(groups[i]))
		}
	}

	end := records

	if end > utils.MaxFlowTopNInsertionGroups {

		end = utils.MaxFlowTopNInsertionGroups
	}

	err = aggregator.writeIndexableColumns(tick, exist, end, endPosition)

	if err != nil {

		return err
	}

	keyElementSize = 0

	count := false

	columnPoolIndex, columns := aggregator.encoder.MemoryPool.AcquireStringPool(aggregator.aggregationColumnElementSize + 1)

	defer aggregator.encoder.MemoryPool.ReleaseStringPool(columnPoolIndex)

	for i := 0; i < aggregator.aggregationColumnElementSize; i++ {

		if aggregator.dataTypes[aggregator.aggregationColumns[i]] == Int64 {

			columns[keyElementSize] = aggregator.aggregationColumns[i] + utils.KeySeparator + utils.Sum

			keyElementSize++

			if !count {

				columns[keyElementSize] = utils.All + utils.KeySeparator + utils.Count

				keyElementSize++

				count = true
			}
		} else if !count {

			columns[keyElementSize] = utils.All + utils.KeySeparator + utils.Count

			keyElementSize++

			count = true
		}
	}

	for columnIndex := range columns[:keyElementSize] {

		from := 0

		to := end

		parts = 1

		if to > utils.OverflowLength {

			parts = int(math.Ceil(float64(end) / float64(utils.OverflowLength)))

			to = utils.OverflowLength

		}

		int64Values := aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[columns[columnIndex]])

		tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireINT64Pool(end)

		aggregator.encoder.MemoryPool.ResetINT64Pool(tempPoolIndex, end, 0)

		elementSize := 0

		aggregator.ordinals.Range(func(position uint32) {

			tempValues[elementSize] = int64Values[int(position)]

			elementSize++
		})

		for partIndex := 0; partIndex < parts; partIndex++ {

			if partIndex == parts-1 {

				to = end
			}

			err = aggregator.writeValues(tempValues[from:to], partIndex, columns[columnIndex])

			if err != nil {

				aggregator.encoder.MemoryPool.ReleaseINT64Pool(tempPoolIndex)

				return err
			}

			from = to

			to += utils.OverflowLength

		}

		aggregator.encoder.MemoryPool.ReleaseINT64Pool(tempPoolIndex)
	}

	err = aggregator.commit(store)

	if err != nil {

		return err
	}

	// after commiting the data in the store we need to update the parts. Since part is started with 1 we need to add the remaining parts added in the store
	if parts > 1 {

		if utils.TraceEnabled() {

			aggregator.logger.Trace(fmt.Sprintf("store %v , tick %v , part %v", store.GetName(), tick, parts))

		}

		//Use set multipart key because add multipart key adds the part everytime
		err = store.SetMultipartKey(binary.BigEndian.Uint64(aggregator.int64ValueBytes), uint16(parts-1))

		if err != nil {

			return err
		}

	}

	aggregator.pending = false

	return nil
}

func (aggregator *EventAggregator) writeIndexableColumns(tick int32, exist bool, end, endPosition int) error {

	ordinalPoolIndex := -1

	var err error

	var ordinals []int32

	for columnIndex, column := range aggregator.indexableColumns[:aggregator.indexableColumnElementSize] {

		part := StringToINT64(defaultPartIndex)

		eventId := utils.GetAggregationEventId(tick, StringToUINT16(defaultPartIndex))

		mappingStore := datastore.GetStore(aggregator.indexableColumns[columnIndex]+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, aggregator.encoder, aggregator.tokenizers[1])

		if mappingStore == nil {

			return errors.New(fmt.Sprintf(utils.ErrorAcquireStore, aggregator.indexableColumns[columnIndex]+utils.HyphenSeparator+datastore.Mappings))
		}

		if aggregator.dataTypes[aggregator.indexableColumns[columnIndex]] == String {

			stringValues := aggregator.encoder.MemoryPool.GetStringPool(aggregator.poolIndices[aggregator.indexableColumns[columnIndex]])

			tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireStringPool(end)

			elementSize, records := 0, 0

			for valuePosition := range stringValues {

				if aggregator.ordinals.Contains(uint32(valuePosition)) {

					tempValues[elementSize] = stringValues[valuePosition]

					if _, ok := aggregator.stringTokens[columnIndex][stringValues[valuePosition]]; !ok {

						aggregator.stringTokens[columnIndex][stringValues[valuePosition]] = map[int64]struct{}{}
					}

					aggregator.stringTokens[columnIndex][stringValues[valuePosition]][eventId] = struct{}{}

					elementSize++

					records++

					if records == utils.OverflowLength {

						records = 0

						part = part + 1

						eventId = utils.GetAggregationEventId(tick, uint16(part))
					}

				} else if exist && valuePosition < endPosition {

					if _, ok := aggregator.passoverStringTokens[columnIndex][stringValues[valuePosition]]; !ok {

						aggregator.passoverStringTokens[columnIndex][stringValues[valuePosition]] = map[int64]struct{}{}
					}

					aggregator.passoverStringTokens[columnIndex][stringValues[valuePosition]][eventId] = struct{}{}

				}
			}

			err, ordinalPoolIndex, ordinals = mappingStore.MapStringValues(tempPoolIndex, aggregator.encoder, elementSize)

			aggregator.encoder.MemoryPool.ReleaseStringPool(tempPoolIndex)

			if err != nil {

				aggregator.encoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)

				return err
			}
		} else {

			int64Values := aggregator.encoder.MemoryPool.GetINT64Pool(aggregator.poolIndices[aggregator.indexableColumns[columnIndex]])

			tempPoolIndex, tempValues := aggregator.encoder.MemoryPool.AcquireINT64Pool(end)

			aggregator.encoder.MemoryPool.ResetINT64Pool(tempPoolIndex, end, 0)

			elementSize, records := 0, 0

			for valuePosition := range int64Values {

				if aggregator.ordinals.Contains(uint32(valuePosition)) {

					tempValues[elementSize] = int64Values[valuePosition]

					if _, ok := aggregator.numericTokens[columnIndex][int64Values[valuePosition]]; !ok {

						aggregator.numericTokens[columnIndex][int64Values[valuePosition]] = map[int64]struct{}{}
					}

					aggregator.numericTokens[columnIndex][int64Values[valuePosition]][eventId] = struct{}{}

					elementSize++

					records++

					if records == utils.OverflowLength {

						records = 0

						part = part + 1

						eventId = utils.GetAggregationEventId(tick, uint16(part))
					}

				} else if exist && valuePosition < endPosition {

					if _, ok := aggregator.passoverNumericTokens[columnIndex][int64Values[valuePosition]]; !ok {

						aggregator.passoverNumericTokens[columnIndex][int64Values[valuePosition]] = map[int64]struct{}{}
					}

					aggregator.passoverNumericTokens[columnIndex][int64Values[valuePosition]][eventId] = struct{}{}
				}
			}

			err, ordinalPoolIndex, ordinals = mappingStore.MapNumericValues(tempPoolIndex, aggregator.encoder, elementSize)

			aggregator.encoder.MemoryPool.ReleaseINT64Pool(tempPoolIndex)

			if err != nil {

				aggregator.encoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)

				return err
			}
		}

		err = aggregator.writeOrdinals(ordinals, end, column)

		if err != nil {

			aggregator.encoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)

			return err
		}

		aggregator.encoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)
	}

	return nil
}

func (aggregator *EventAggregator) writeOrdinals(ordinals []int32, end int, column string) error {

	from := 0

	to := end

	parts := 1

	if to > utils.OverflowLength {

		parts = int(math.Ceil(float64(end) / float64(utils.OverflowLength)))

		to = utils.OverflowLength

	}

	for partIndex := 0; partIndex < parts; partIndex++ {

		if partIndex == parts-1 {

			to = end
		}

		bytePoolIndex, bufferBytes, err := aggregator.encoder.EncodeINT32Values(ordinals[from:to], None, Int32, GetDataTypeBits(Int32), utils.MaxValueBytes)

		if err != nil {

			return err
		}

		err = aggregator.writeTxn([]byte(aggregator.tick+utils.KeySeparator+column+utils.OrdinalSuffix+utils.KeySeparator+INTToStringValue(partIndex)), bufferBytes)

		aggregator.encoder.MemoryPool.ReleaseBytePool(bytePoolIndex)

		if err != nil {

			return err

		}

		from = to

		to += utils.OverflowLength

	}

	return nil

}

func (aggregator *EventAggregator) writeValues(int64Values []int64, part int, column string) error {

	dataType := GetMaxDataTypeINT64Values(int64Values)

	var bufferBytes []byte

	bytePoolIndex := utils.NotAvailable

	var err error

	switch {

	case dataType == Int8:

		poolIndex, values := aggregator.encoder.MemoryPool.AcquireINT8Pool(len(int64Values))

		aggregator.encoder.MemoryPool.ResetINT8Pool(poolIndex, len(values), 0)

		INT64ToINT8Values(int64Values, values)

		bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeINT8Values(None, values, utils.MaxValueBytes)

		aggregator.encoder.MemoryPool.ReleaseINT8Pool(poolIndex)

	case dataType == Int16:

		poolIndex, values := aggregator.encoder.MemoryPool.AcquireINT16Pool(len(int64Values))

		aggregator.encoder.MemoryPool.ResetINT16Pool(poolIndex, len(values), 0)

		INT64ToINT16Values(int64Values, values)

		bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeINT16Values(None, values, utils.MaxValueBytes)

		aggregator.encoder.MemoryPool.ReleaseINT16Pool(poolIndex)

	case dataType == Int24 || dataType == Int32:

		poolIndex, values := aggregator.encoder.MemoryPool.AcquireINT32Pool(len(int64Values))

		aggregator.encoder.MemoryPool.ResetINT32Pool(poolIndex, len(values), 0)

		INT64ToINT32Values(int64Values, values)

		bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeINT32Values(values, None, dataType, GetDataTypeBits(dataType), utils.MaxValueBytes)

		aggregator.encoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	case dataType == Int40 || dataType == Int48 || dataType == Int56 || dataType == Int64:

		bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeINT64Values(int64Values, None, dataType, utils.MaxValueBytes)

	}

	defer aggregator.encoder.MemoryPool.ReleaseBytePool(bytePoolIndex)

	if err != nil {

		return err
	}

	return aggregator.writeTxn([]byte(aggregator.tick+utils.KeySeparator+column+utils.KeySeparator+INTToStringValue(part)), bufferBytes)
}

/* ------------------------------------- Unpack & Read -----------------------------------------------------*/

func (aggregator *EventAggregator) unpack(bufferBytes []byte) error {

	if len(bufferBytes) == 0 || !bytes.Equal(bufferBytes[len(bufferBytes)-3:], utils.EOTBytes) {

		return errors.New("invalid/corrupted buffer bytes")

	}

	position, length := 0, 0

	//decode indexable columns

	bufferLength := int(binary.LittleEndian.Uint32(bufferBytes[position : position+4]))

	position += 4

	startPosition := position

	ordinal := int32(0)

	key := utils.Empty

	for position < startPosition+bufferLength {

		length = int(binary.LittleEndian.Uint16(bufferBytes[position : position+2]))

		position += 2

		aggregator.indexableColumns[aggregator.indexableColumnElementSize] = string(bufferBytes[position : position+length])

		position += length

		aggregator.dataTypes[aggregator.indexableColumns[aggregator.indexableColumnElementSize]] = DataType(bufferBytes[position : position+1][0])

		position++

		aggregator.indexableColumnElementSize++
	}

	//decode aggregated columns
	bufferLength = int(binary.LittleEndian.Uint32(bufferBytes[position : position+4]))

	position += 4

	startPosition = position

	columnElementSize := 0

	aggregator.columns[INTToStringValue(utils.NotAvailable)] = utils.All

	for position < startPosition+bufferLength {

		length = int(binary.LittleEndian.Uint16(bufferBytes[position : position+2]))

		position += 2

		aggregator.aggregationColumns[aggregator.aggregationColumnElementSize] = string(bufferBytes[position : position+length])

		position += length

		aggregator.dataTypes[aggregator.aggregationColumns[aggregator.aggregationColumnElementSize]] = DataType(bufferBytes[position : position+1][0])

		position++

		aggregator.columns[INTToStringValue(columnElementSize)] = aggregator.aggregationColumns[aggregator.aggregationColumnElementSize]

		columnElementSize++

		aggregator.aggregationColumnElementSize++
	}

	//decode ordinals

	bufferLength = int(binary.LittleEndian.Uint32(bufferBytes[position : position+4]))

	position += 4

	startPosition = position

	value := utils.Empty

	for position < startPosition+bufferLength {

		//decode ordinal
		ordinal = int32(binary.LittleEndian.Uint32(bufferBytes[position : position+4]))

		position += 4

		//decode value

		length = int(binary.LittleEndian.Uint16(bufferBytes[position : position+2]))

		position += 2

		value = string(bufferBytes[position : position+length])

		aggregator.valuesByOrdinal.Put(ordinal, value)

		aggregator.ordinalsByValue.Put(value, ordinal)

		position += length
	}

	//decode aggregations

	for position < len(bufferBytes)-3 {

		//decode column
		length = int(binary.LittleEndian.Uint16(bufferBytes[position : position+2]))

		position += 2

		key = string(bufferBytes[position : position+length]) //column

		position += length

		//value

		aggregator.aggregations.Put(key, int64(binary.LittleEndian.Uint64(bufferBytes[position:position+8])))

		position += 8
	}

	return nil
}

func (aggregator *EventAggregator) read(fileName string) ([]byte, error) {

	if utils.TraceEnabled() {

		aggregator.logger.Trace(fmt.Sprintf("aggregator %v started reading file %v", aggregator.aggregatorId, fileName))
	}

	file, err := os.OpenFile(fileName, os.O_RDWR, 0666)

	defer func(file *os.File) {

		if file != nil {

			_ = file.Close()
		}

		if utils.TraceEnabled() {

			aggregator.logger.Trace(fmt.Sprintf("aggregator %v deleting file %v", aggregator.aggregatorId, fileName))
		}

		err = os.Remove(fileName)

		if err != nil {

			aggregator.logger.Error(fmt.Sprintf("aggregator %v failed to delete file %v, reason: %v", aggregator.aggregatorId, fileName, err))
		}
	}(file)

	if err != nil {

		return nil, err
	}

	var stat os.FileInfo

	var bufferBytes []byte

	if stat, err = file.Stat(); err == nil && int(stat.Size()) > len(aggregator.bufferBytes) {

		bufferBytes, err = os.ReadFile(fileName)

		if err != nil {

			return nil, err
		}

		return gozstd.Decompress(aggregator.decodedBytes[:0], bufferBytes)
	}

	length, err := file.ReadAt(aggregator.bufferBytes, 0)

	if err != nil && !strings.EqualFold(err.Error(), "EOF") {

		return nil, err
	}

	return gozstd.Decompress(aggregator.decodedBytes[:0], aggregator.bufferBytes[:length])

}

/* ------------------------------------- Cleanup -----------------------------------------------------*/

func (aggregator *EventAggregator) cleanup() {

	aggregator.pending = false

	aggregator.records = 0

	for i := range aggregator.stringTokens {

		clear(aggregator.stringTokens[i])

		clear(aggregator.numericTokens[i])

		clear(aggregator.passoverStringTokens[i])

		clear(aggregator.passoverNumericTokens[i])
	}

	clear(aggregator.eventIds)

	aggregator.indexableColumnElementSize, aggregator.aggregationColumnElementSize = 0, 0

	clear(aggregator.columns)

	aggregator.cleanupPool()

	aggregator.cleanupMappings()

	aggregator.valuesByOrdinal.Clear()

	aggregator.ordinalsByValue.Clear()

	aggregator.aggregations.Clear()

	aggregator.groups.Clear()

	aggregator.probes.Clear()

	clear(aggregator.dataTypes)

	clear(aggregator.poolIndices)

	aggregator.cleanupTxn()

}

func (aggregator *EventAggregator) cleanupMappings() {

	aggregator.stringMappings.Clear()

	aggregator.numericMappings.Clear()
}

func (aggregator *EventAggregator) cleanupPool() {

	for column, poolIndex := range aggregator.poolIndices {

		delete(aggregator.poolIndices, column)

		if dataType, ok := aggregator.dataTypes[column]; ok {

			if dataType == String {

				aggregator.encoder.MemoryPool.ReleaseStringPool(poolIndex)
			} else if dataType == Int64 {

				aggregator.encoder.MemoryPool.ReleaseINT64Pool(poolIndex)

			} else if dataType == Int32 {

				aggregator.encoder.MemoryPool.ReleaseINT32Pool(poolIndex)
			}
		}
	}
}

/* ------------------------------------- Posting List -----------------------------------------------------*/

func (aggregator *EventAggregator) writeStringPostingListIndex(tick int32, intervalMinutes, columnIndex int, column, plugin string, indexType utils.DatastoreType) error {

	storeName := datastore.GetStoreName(tick, column+utils.HyphenSeparator+plugin, datastore.HorizontalStore) + utils.HyphenSeparator + INTToStringValue(int(String)) + utils.HyphenSeparator + INTToStringValue(intervalMinutes)

	store := datastore.GetStore(storeName, indexType, true, true, aggregator.encoder, aggregator.tokenizers[1])

	if store == nil {

		return errors.New(fmt.Sprintf(utils.ErrorAcquireStore, storeName))
	}

	for token, eventIds := range aggregator.stringTokens[columnIndex] {

		if token == utils.Empty {

			continue
		}

		aggregator.keyBuffers[0] = []byte(strings.ToLower(token))

		found, valueBytes, err := store.Get(aggregator.keyBuffers[0], aggregator.valueBuffers[0], aggregator.encoder, aggregator.event, aggregator.waitGroup, aggregator.tokenizers[1], false)

		if err != nil {

			if !strings.Contains(err.Error(), utils.ErrorIsDeleted) {

				store.Delete(aggregator.keyBuffers[0], aggregator.encoder, aggregator.tokenizers[1])
			}

			found = false
		}

		bytePoolIndex := utils.NotAvailable

		poolIndex := utils.NotAvailable

		var int64Values []int64

		var bufferBytes []byte

		if found && len(valueBytes) > 0 {

			poolIndex, int64Values, err = aggregator.decoder.DecodeINT64Values(GetEncoding(valueBytes[0]), GetDataType(valueBytes[0]), valueBytes[1:], token, store.GetName(), 0)

			if err != nil {

				if poolIndex != utils.NotAvailable {

					aggregator.decoder.MemoryPool.ReleaseINT64Pool(poolIndex)
				}

				aggregator.logger.Error(fmt.Sprintf(utils.ErrorUpdatePostingListIndex, token, store.GetName(), err.Error()))

				continue
			}

			clear(aggregator.eventIds)

			for _, value := range int64Values {

				aggregator.eventIds[value] = struct{}{}
			}

			if passOverEventIds, ok := aggregator.passoverStringTokens[columnIndex][token]; ok {

				for eventId := range passOverEventIds {

					delete(aggregator.eventIds, eventId)

				}

			}

			for eventId := range eventIds {

				aggregator.eventIds[eventId] = struct{}{}
			}

			aggregator.encoder.MemoryPool.ReleaseINT64Pool(poolIndex)

			// rare situation where all eventids discarded but need to handle it to prevent filter query
			if len(aggregator.eventIds) == 0 {

				continue
			}

			valuePoolIndex, values := aggregator.encoder.MemoryPool.AcquireINT64Pool(len(aggregator.eventIds))

			aggregator.encoder.MemoryPool.ResetINT64Pool(valuePoolIndex, len(values), 0)

			position := 0

			for eventId := range aggregator.eventIds {

				values[position] = eventId

				position++
			}

			utils.SortINT64Values(values[:position])

			bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeINT64Values(values[:position], None, Int64, utils.MaxValueBytes)

			aggregator.encoder.MemoryPool.ReleaseINT64Pool(valuePoolIndex)
		} else {

			poolIndex, int64Values = aggregator.encoder.MemoryPool.AcquireINT64Pool(len(eventIds))

			position := 0

			for eventId := range eventIds {

				int64Values[position] = eventId

				position++
			}

			utils.SortINT64Values(int64Values[:position])

			bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeINT64Values(int64Values[:position], None, Int64, utils.MaxValueBytes)

			if poolIndex != utils.NotAvailable {

				aggregator.encoder.MemoryPool.ReleaseINT64Pool(poolIndex)
			}
		}

		if bytePoolIndex != utils.NotAvailable && len(bufferBytes) > 0 {

			err = store.Put(aggregator.keyBuffers[0], bufferBytes, aggregator.encoder, aggregator.tokenizers[1])

			if err != nil {

				aggregator.logger.Error(fmt.Sprintf(utils.ErrorWriteKey, token, store.GetName(), err.Error()))
			}

			aggregator.encoder.MemoryPool.ReleaseBytePool(bytePoolIndex)
		}
	}

	return nil
}

func (aggregator *EventAggregator) writeNumericPostingListIndex(tick int32, intervalMinutes, columnIndex int, column, plugin string, indexType utils.DatastoreType) error {

	storeName := datastore.GetStoreName(tick, column+utils.HyphenSeparator+plugin, datastore.HorizontalStore) + utils.HyphenSeparator + INTToStringValue(int(Int64)) + utils.HyphenSeparator + INTToStringValue(intervalMinutes)

	store := datastore.GetStore(storeName, indexType, true, true, aggregator.encoder, aggregator.tokenizers[1])

	if store == nil {

		return errors.New(fmt.Sprintf(utils.ErrorAcquireStore, storeName))
	}

	for token, eventIds := range aggregator.numericTokens[columnIndex] {

		binary.BigEndian.PutUint64(aggregator.int64ValueBytes, uint64(token))

		found, valueBytes, err := store.Get(aggregator.int64ValueBytes, aggregator.valueBuffers[0], aggregator.encoder, aggregator.event, aggregator.waitGroup, aggregator.tokenizers[1], false)

		if err != nil {

			if !strings.Contains(err.Error(), utils.ErrorIsDeleted) {

				store.Delete(aggregator.keyBuffers[0], aggregator.encoder, aggregator.tokenizers[1])
			}

			found = false
		}

		bytePoolIndex := utils.NotAvailable

		poolIndex := utils.NotAvailable

		var int64Values []int64

		var bufferBytes []byte

		if found && len(valueBytes) > 0 {

			poolIndex, int64Values, err = aggregator.decoder.DecodeINT64Values(GetEncoding(valueBytes[0]), GetDataType(valueBytes[0]), valueBytes[1:], INT64ToStringValue(token), store.GetName(), 0)

			if err != nil {

				if poolIndex != utils.NotAvailable {

					aggregator.decoder.MemoryPool.ReleaseINT64Pool(poolIndex)
				}

				aggregator.logger.Error(fmt.Sprintf(utils.ErrorUpdatePostingListIndex, token, store.GetName(), err.Error()))

				continue
			}

			clear(aggregator.eventIds)

			for _, value := range int64Values {

				aggregator.eventIds[value] = struct{}{}
			}

			if passOverEventIds, ok := aggregator.passoverNumericTokens[columnIndex][token]; ok {

				for eventId := range passOverEventIds {

					delete(aggregator.eventIds, eventId)

				}

			}

			for eventId := range eventIds {

				aggregator.eventIds[eventId] = struct{}{}
			}

			aggregator.encoder.MemoryPool.ReleaseINT64Pool(poolIndex)

			// rare situation where all eventids discarded but need to handle it to prevent filter query
			if len(aggregator.eventIds) == 0 {

				continue
			}

			valuePoolIndex, values := aggregator.encoder.MemoryPool.AcquireINT64Pool(len(aggregator.eventIds))

			aggregator.encoder.MemoryPool.ResetINT64Pool(valuePoolIndex, len(values), 0)

			position := 0

			for eventId := range aggregator.eventIds {

				values[position] = eventId

				position++
			}

			utils.SortINT64Values(values[:position])

			bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeINT64Values(values[:position], None, Int64, utils.MaxValueBytes)

			aggregator.encoder.MemoryPool.ReleaseINT64Pool(valuePoolIndex)
		} else {

			poolIndex, int64Values = aggregator.encoder.MemoryPool.AcquireINT64Pool(len(eventIds))

			position := 0

			for eventId := range eventIds {

				int64Values[position] = eventId

				position++
			}

			utils.SortINT64Values(int64Values[:position])

			bytePoolIndex, bufferBytes, err = aggregator.encoder.EncodeINT64Values(int64Values[:position], None, Int64, utils.MaxValueBytes)

			if poolIndex != utils.NotAvailable {

				aggregator.encoder.MemoryPool.ReleaseINT64Pool(poolIndex)
			}
		}

		if bytePoolIndex != utils.NotAvailable && len(bufferBytes) > 0 {

			err = store.Put(aggregator.int64ValueBytes, bufferBytes, aggregator.encoder, aggregator.tokenizers[1])

			if err != nil {

				aggregator.logger.Error(fmt.Sprintf(utils.ErrorWriteKey, token, store.GetName(), err.Error()))
			}

			aggregator.encoder.MemoryPool.ReleaseBytePool(bytePoolIndex)
		}
	}

	return nil
}

func (aggregator *EventAggregator) setColumnContext(groups int) {

	count := false

	for _, column := range aggregator.aggregationColumns[:aggregator.aggregationColumnElementSize] {

		if aggregator.dataTypes[column] == String {

			if !count {

				aggregator.poolIndices[utils.All+utils.KeySeparator+utils.Count], _ = aggregator.encoder.MemoryPool.AcquireINT64Pool(groups)

				aggregator.encoder.MemoryPool.ResetINT64Pool(aggregator.poolIndices[utils.All+utils.KeySeparator+utils.Count], groups, 0)

				aggregator.dataTypes[utils.All+utils.KeySeparator+utils.Count] = Int64

				count = true
			}
		} else {

			aggregator.poolIndices[column+utils.KeySeparator+utils.Sum], _ = aggregator.encoder.MemoryPool.AcquireINT64Pool(groups)

			aggregator.encoder.MemoryPool.ResetINT64Pool(aggregator.poolIndices[column+utils.KeySeparator+utils.Sum], groups, 0)

			aggregator.dataTypes[column+utils.KeySeparator+utils.Sum] = Int64

			if !count {

				aggregator.poolIndices[utils.All+utils.KeySeparator+utils.Count], _ = aggregator.encoder.MemoryPool.AcquireINT64Pool(groups)

				aggregator.encoder.MemoryPool.ResetINT64Pool(aggregator.poolIndices[utils.All+utils.KeySeparator+utils.Count], groups, 0)

				aggregator.dataTypes[utils.All+utils.KeySeparator+utils.Count] = Int64

				count = true
			}
		}
	}

	for _, column := range aggregator.indexableColumns[:aggregator.indexableColumnElementSize] {

		if aggregator.dataTypes[column] == String {

			aggregator.poolIndices[column], _ = aggregator.encoder.MemoryPool.AcquireStringPool(groups)

		} else {

			aggregator.poolIndices[column], _ = aggregator.encoder.MemoryPool.AcquireINT64Pool(groups)

			aggregator.encoder.MemoryPool.ResetINT64Pool(aggregator.poolIndices[column], groups, 0)
		}
	}
}

/* ------------------------------------- Common -----------------------------------------------------*/

func getIndexStoreType(storeType utils.DatastoreType) utils.DatastoreType {

	indexType := utils.LogIndex

	if storeType == utils.FlowAggregation {

		indexType = utils.FlowIndex

	} else if storeType == utils.PolicyAggregation {

		indexType = utils.PolicyIndex

	} else if storeType == utils.TrapAggregation {

		indexType = utils.TrapIndex
	}

	return indexType
}

//txn

func (aggregator *EventAggregator) writeTxn(keyBytes, bufferBytes []byte) (err error) {

	if size := len(bufferBytes) + 4 + len(utils.EOTBytes) + len(keyBytes) + aggregator.txnOffset; size > len(aggregator.txnBufferBytes) {

		aggregator.logger.Info(fmt.Sprintf("remapping annonymous txn buffers with current length %v and required size %v", len(aggregator.txnBufferBytes), size))

		aggregator.txnBufferBytes = utils.RemapBytes(aggregator.txnBufferBytes, size)
	}

	copy(bufferBytes, utils.CheckSumV1Bytes) // add checksum

	WriteINT32Value(int32(len(bufferBytes)-utils.MaxValueBytes), 4, bufferBytes) //add length

	WriteINT32Value(int32(len(keyBytes)), 0, aggregator.int32Bytes) //calculate length of key bytes

	copy(aggregator.txnBufferBytes[aggregator.txnOffset:], aggregator.int32Bytes) //add key length bytes

	aggregator.txnOffset += 4

	copy(aggregator.txnBufferBytes[aggregator.txnOffset:], keyBytes)

	aggregator.txnOffset += len(keyBytes)

	offset := aggregator.txnOffset

	copy(aggregator.txnBufferBytes[aggregator.txnOffset:], bufferBytes)

	aggregator.txnOffset += len(bufferBytes)

	aggregator.txnEntries[utils.GetHash64(keyBytes)] = utils.TxnEntry{Length: len(bufferBytes), Offset: offset}

	return
}

func (aggregator *EventAggregator) cleanupTxn() {

	aggregator.txnOffset, aggregator.txnPartition = 4, -1

	clear(aggregator.txnEntries)
}

func (aggregator *EventAggregator) commit(store *storage.Store) error {

	copy(aggregator.txnBufferBytes[aggregator.txnOffset:], utils.EOTBytes)

	aggregator.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(aggregator.txnOffset-4), 0, aggregator.txnBufferBytes)

	return store.CommitTxn(aggregator.txnBufferBytes[:aggregator.txnOffset], aggregator.txnEntries, aggregator.encoder, aggregator.txnPartition)
}
