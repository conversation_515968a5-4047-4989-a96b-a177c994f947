/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/*
	This class is used to write data in horizontal format, specifically event data.
	Here we write log, flow, trap, event policy, metric policy, policy result, status flap history, policy flap history, and runbook worklog plugin.
*/

/* Change Logs:
* Date                   Author          		Notes
* 2025-02-10			 Dhaval <PERSON>ra			<PERSON>tad<PERSON>-4913  Altered modulo operator with new modulo function
* 2025-02-28			 Dhaval <PERSON>ra			<PERSON>-5194  Changed field to "event.source" for Runbook Worklog plugin
* 2025-03-05			 <PERSON><PERSON><PERSON>-5190  Migrated constants from datastore to utils to match SonarQube Standard
* 2025-05-02             Vedant <PERSON><PERSON><PERSON>-6080  For initial 1 lakh records restricted the writing of invalid indexable column
* 2025-04-05			 Dhaval Bera			Motadata-6076  Added Length Checks Before Batch Insertion
* 2025-05-05			 Swapnil A. Dave		MOTADATA-6078 passed defaultblobpool const as the function parameter in memory pool initialization
* 2025-05-08  			 Hardik Vala			MOTADATA-6073 Updated trim behaviour for Encoding String Values, discard default indexer column
* 2025-05-10             Dhaval Bera            MOTADATA-6174  Added Unmap function to release memory-mapped resources
 */

package writer

import (
	bytes2 "bytes"
	"encoding/binary"
	"errors"
	"fmt"
	"golang.org/x/sys/unix"
	"math"
	. "motadatadatastore/codec"
	"motadatadatastore/datastore"
	. "motadatadatastore/storage"
	"motadatadatastore/utils"
	"os"
	"regexp"
	"runtime"
	"strings"
	"sync"
	"sync/atomic"
)

var (
	horizontalWriterLogger = utils.NewLogger("Horizontal Writer", "writer")

	horizontalEvents = utils.EventDir + utils.PathSeparator + utils.HorizontalFormat

	healthCheckupEvents = utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.HealthStatistics

	// regex for URL
	regex = regexp.MustCompile("^(http:\\/\\/www\\.|https:\\/\\/www\\.|http:\\/\\/|https:\\/\\/|\\/|\\/\\/)?(((25[0-5]|(2[0-4]|1\\d|[1-9]|)\\d)\\.?\\b){4})?([A-z0-9_-]*?[:]?[A-z0-9_-]*?[@]?[A-z0-9]+([\\-\\.]{1}[a-z0-9]+)*\\.[a-z]{2,5}(:[0-9]{1,5})?)?(\\/.*)?$")
)

const (
	indexFieldMaxLength = 50

	MessageWriterId = " for writerId %v"
)

type HorizontalWriter struct {
	tokenizer *utils.Tokenizer //used in store funcs

	waitGroup *sync.WaitGroup

	compositeStore *Store

	event DiskIOEvent

	blobEvent datastore.BlobEvent

	encoder Encoder

	decoder Decoder

	dataStoreType utils.DatastoreType

	Events chan utils.MotadataMap

	ShutdownNotifications chan bool

	txnEntries, compositeTxnEntries map[uint64]utils.TxnEntry

	stringFields map[string][]string

	int64Fields map[string][]int64

	int32Fields map[string][]int32

	probes map[string]int

	columns map[string]byte

	nonIndexableFields map[string]interface{}

	keyBytes, valueBytes, int32Bytes []byte

	bufferBytes, valueBufferBytes, txnBufferBytes, compositeTxnBufferBytes []byte

	int8Values []int8

	err error

	plugin string

	position, writerId, batchSize, valueElementSize, overflowPoolLength, length int

	tick, Pending int32

	txnPartition, compositeTxnPartition, txnOffset, compositeTxnOffset int

	shutdown bool
}

func NewHorizontalWriter(writerId int) *HorizontalWriter {

	pool := utils.NewMemoryPool(10, utils.MaxPoolLength, false, utils.DefaultBlobPools)

	encoder := NewEncoder(pool)

	decoder := NewDecoder(pool)

	bytes, err := unix.Mmap(-1, 0, 10_000*utils.MaxStringBytes, unix.PROT_READ|unix.PROT_WRITE, unix.MAP_PRIVATE|unix.MAP_ANON)

	if err != nil {

		horizontalWriterLogger.Error(fmt.Sprintf("error %v occurred while mapping annonymous buffer for horizontal writer %v", err, writerId))

		bytes = make([]byte, 10_000*utils.MaxStringBytes)
	}

	txnBufferBytes, err := unix.Mmap(-1, 0, utils.GetDataWriterTxnBufferBytes(), unix.PROT_READ|unix.PROT_WRITE, unix.MAP_PRIVATE|unix.MAP_ANON)

	if err != nil {

		horizontalWriterLogger.Error(fmt.Sprintf("error %v occurred while mapping annonymous txn buffer for horizontal writer %v", err, writerId))

		txnBufferBytes = make([]byte, utils.GetDataWriterTxnBufferBytes())
	}

	compositeTxnBufferBytes, err := unix.Mmap(-1, 0, utils.GetDataWriterTxnBufferBytes(), unix.PROT_READ|unix.PROT_WRITE, unix.MAP_PRIVATE|unix.MAP_ANON)

	if err != nil {

		horizontalWriterLogger.Error(fmt.Sprintf("error %v occurred while mapping annonymous composite txn buffer for horizontal writer %v", err, writerId))

		compositeTxnBufferBytes = make([]byte, utils.GetDataWriterTxnBufferBytes())
	}

	return &HorizontalWriter{

		Events: make(chan utils.MotadataMap, 1_00_00),

		writerId: writerId,

		encoder: encoder,

		decoder: decoder,

		event: DiskIOEvent{},

		blobEvent: datastore.BlobEvent{},

		valueBytes: bytes,

		ShutdownNotifications: make(chan bool, 5),

		int32Fields: make(map[string][]int32, 100),

		int64Fields: make(map[string][]int64, 100),

		stringFields: make(map[string][]string, 100),

		probes: make(map[string]int, 100),

		nonIndexableFields: map[string]interface{}{},

		columns: make(map[string]byte, 100),

		int32Bytes: make([]byte, 4),

		waitGroup: &sync.WaitGroup{},

		int8Values: make([]int8, 1),

		txnBufferBytes: txnBufferBytes,

		compositeTxnBufferBytes: compositeTxnBufferBytes,

		txnEntries: make(map[uint64]utils.TxnEntry, 50),

		compositeTxnEntries: make(map[uint64]utils.TxnEntry, 50),

		txnPartition: utils.NotAvailable,

		compositeTxnPartition: utils.NotAvailable,

		txnOffset: 4, //initial 4 bytes for number of bytes

		compositeTxnOffset: 4, //initial 4 bytes for number of bytes
	}

}

func (writer *HorizontalWriter) Start() {

	bytes, err := unix.Mmap(-1, 0, utils.GetDataWriterValueBufferBytes(), unix.PROT_READ|unix.PROT_WRITE, unix.MAP_PRIVATE|unix.MAP_ANON)

	if err != nil {

		horizontalWriterLogger.Error(fmt.Sprintf("error %v occurred while mapping annonymous buffer for horizontal writer %v value buffer", err, writer.writerId))

		bytes = make([]byte, utils.GetDataWriterValueBufferBytes())
	}

	writer.valueBufferBytes = bytes

	writer.tokenizer = &utils.Tokenizer{

		Tokens: make([]string, utils.TokenizerLength),
	}

	go func() {

		utils.WriterEngineShutdownMutex.Add(1)

		for {

			if writer.shutdown || utils.GlobalShutdown {

				break
			}

			writer.processRequest()
		}

		utils.WriterEngineShutdownMutex.Done()

	}()

}

func (writer *HorizontalWriter) processRequest() {

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			horizontalWriterLogger.Error(fmt.Sprintf("error %v occurred in horizontal writer %v", err, writer.writerId))

			horizontalWriterLogger.Error(fmt.Sprintf("!!!STACK TRACE for horizontal writer %v!!! \n %v", writer.writerId, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

			horizontalWriterLogger.Error("Horizontal writer restarted")

		}

	}()

	for {

		select {

		case event := <-writer.Events:

			writer.writeHorizontalBatch(event)

		case <-writer.ShutdownNotifications:

			horizontalWriterLogger.Info(fmt.Sprintf("shutting down horizontal writer %v", writer.writerId))

			if err := unix.Munmap(writer.valueBytes); err != nil {

				horizontalWriterLogger.Warn(fmt.Sprintf("failed to unmap anonymous mapped buffer,reason: %v", err.Error()))
			}

			if err := unix.Munmap(writer.valueBufferBytes); err != nil {

				horizontalWriterLogger.Warn(fmt.Sprintf("failed to unmap anonymous mapped buffer,reason: %v", err.Error()))
			}

			if err := unix.Munmap(writer.txnBufferBytes); err != nil {

				horizontalWriterLogger.Warn(fmt.Sprintf("failed to unmap anonymous mapped txn buffer,reason: %v", err.Error()))
			}

			if err := unix.Munmap(writer.compositeTxnBufferBytes); err != nil {

				horizontalWriterLogger.Warn(fmt.Sprintf("failed to unmap anonymous mapped composite txn buffer,reason: %v", err.Error()))
			}

			writer.encoder.MemoryPool.Unmap()

			writer.shutdown = true

			return
		}
	}

}

// to reset the writer
func (writer *HorizontalWriter) cleanup() {

	writer.cleanupTxn()

	clear(writer.probes)

	clear(writer.columns)

	clear(writer.int32Fields)

	clear(writer.int64Fields)

	clear(writer.stringFields)

	clear(writer.nonIndexableFields)

	writer.valueElementSize = utils.NotAvailable

	writer.position = 0

	writer.plugin = utils.Empty

	writer.batchSize = 0

	writer.bufferBytes = nil

	writer.compositeStore = nil
}

func (writer *HorizontalWriter) cleanupTxn() {

	writer.txnOffset, writer.compositeTxnOffset, writer.txnPartition, writer.compositeTxnPartition = 4, 4, -1, -1

	clear(writer.txnEntries)

	clear(writer.compositeTxnEntries)
}

func (writer *HorizontalWriter) writeHorizontalBatch(batch utils.MotadataMap) {

	fileName := batch.GetStringValue(utils.File)

	writer.dataStoreType = batch[datastore.DatastoreType].(utils.DatastoreType)

	writer.tick = utils.UnixToSeconds(batch.GetInt64Value(utils.Tick))

	writer.plugin = batch.GetStringValue(utils.Plugin)

	defer func(fileName string) {

		if writer.dataStoreType == utils.HealthMetric {

			//sending notification to datareader for next file
			utils.HorizontalFormatHealthSyncNotifications <- utils.WriterSyncEvent{

				File: fileName, WriterId: writer.writerId,
			}

		} else {

			//sending notification to datareader for next file
			utils.HorizontalFormatSyncNotifications <- utils.WriterSyncEvent{

				File: fileName, WriterId: writer.writerId,
			}

		}

		atomic.AddInt32(&writer.Pending, -1)

		writer.decoder.MemoryPool.TestPoolLeak()

		if !utils.GlobalShutdown && utils.PublisherNotifications != nil {

			var objects []string

			if writer.dataStoreType == utils.MetricPolicy || writer.dataStoreType == utils.StatusFlapHistory || writer.dataStoreType == utils.PolicyFlapHistory {

				objects = INT32ToStringValues(writer.int32Fields[utils.ObjectId], make([]string, len(writer.int32Fields[utils.ObjectId])))

			} else if writer.dataStoreType != utils.EventPolicy {

				objects = writer.stringFields[utils.EventSource]

			}

			if objects != nil {

				// publishing the notification to MOTADATA for flushed objects
				utils.PublisherNotifications <- utils.MotadataMap{

					utils.OperationType: utils.Flush,

					utils.EventContext: utils.MotadataMap{

						datastore.Object: objects,

						utils.Plugin: writer.plugin,
					},
				}
			}
		}

		//notify aggregation job to populate for message contains view
		// aggregation job will discard the request for plugin which don't contain's filters as it's view
		if writer.dataStoreType == utils.Log && datastore.IsHorizontalAggregationFound(writer.plugin) {

			utils.ManagerNotifications <- utils.MotadataMap{

				utils.Plugin:          writer.plugin,
				utils.Tick:            utils.SecondsToUnix(writer.tick),
				utils.DatastoreFormat: utils.HorizontalFormat,
				utils.OperationType:   utils.Flush,
			}
		}

		if writer.dataStoreType == utils.Log && datastore.IsHorizontalAggregationFound(datastore.EventSearchPlugin) {

			utils.ManagerNotifications <- utils.MotadataMap{

				utils.Plugin:          datastore.EventSearchPlugin,
				utils.Tick:            utils.SecondsToUnix(writer.tick),
				utils.DatastoreFormat: utils.HorizontalFormat,
				utils.OperationType:   utils.Flush,
			}
		}

		writer.cleanup()
	}(fileName)

	// This check is for test cases where in test case we send buffer
	if batch.Contains(utils.File) {

		if writer.dataStoreType == utils.HealthMetric {

			writer.err = writer.read(healthCheckupEvents + utils.PathSeparator + fileName)

		} else {

			writer.err = writer.read(horizontalEvents + utils.PathSeparator + fileName)

		}

		if writer.err != nil {

			horizontalWriterLogger.Error(fmt.Sprintf("error %v occurred while read batch file %v", writer.err, batch.GetStringValue(utils.File)))

			return
		}
	} else {

		writer.bufferBytes = batch[utils.Buffer].([]uint8)
	}

	writer.updateIndexableColumns()

	// reading all columns from batch
	for writer.position < len(writer.bufferBytes) && len(writer.bufferBytes[writer.position:]) > 4 {

		length := int(binary.LittleEndian.Uint32(writer.bufferBytes[writer.position : writer.position+4]))

		writer.position += 4

		//Batch is corrupted so don't read the batch
		if length+3 > len(writer.bufferBytes[writer.position:]) || !bytes2.Equal(writer.bufferBytes[writer.position+length:writer.position+length+3], utils.EOTBytes) {

			horizontalWriterLogger.Warn(fmt.Sprintf("invalid batch %v for plugin hence skipping records ", writer.plugin))

			break

		}

		writer.decodeColumns(length)

		writer.position += 3 // skipping EOT bytes
	}

	writer.position = 0

	stringFieldTokens := map[string]map[string]struct{}{}

	numericFieldTokens := map[string]map[int]struct{}{}

	for writer.position < len(writer.bufferBytes) && len(writer.bufferBytes[writer.position:]) > 4 {

		length := int(binary.LittleEndian.Uint32(writer.bufferBytes[writer.position : writer.position+4]))

		writer.position += 4

		//Batch is corrupted so don't read the batch
		if length+3 > len(writer.bufferBytes[writer.position:]) || !bytes2.Equal(writer.bufferBytes[writer.position+length:writer.position+length+3], utils.EOTBytes) {

			horizontalWriterLogger.Warn(fmt.Sprintf("invalid batch %v for plugin hence skipping records ", writer.plugin))

			break

		}

		writer.processHorizontalBatch(length, stringFieldTokens, numericFieldTokens)

		writer.position += 3 // skipping EOT bytes

	}

	writer.overflowPoolLength = utils.LogOverflowLength

	// This field is used to align columns in the absence of a specific column in the record. which changes according the store type
	field := utils.EventSource

	dataType := datastore.StringColumn

	switch writer.dataStoreType {

	case utils.Flow:

		writer.overflowPoolLength = utils.OverflowLength

	case utils.Log:

		if writer.plugin != datastore.LogStatPlugin {

			mappingStore := datastore.GetStore(utils.Plugin+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, writer.encoder, writer.tokenizer)

			if mappingStore == nil {

				horizontalWriterLogger.Error(fmt.Sprintf(utils.ErrorAcquireStore, utils.Plugin+utils.HyphenSeparator+datastore.Mappings) + fmt.Sprintf(MessageWriterId, writer.writerId))

				return
			}

			if !mappingStore.ExistStringMapping(writer.plugin) {

				if err := mappingStore.PutStringMapping(writer.plugin, writer.encoder); err != nil {

					horizontalWriterLogger.Error(fmt.Sprintf(utils.ErrorWriteKey, writer.plugin, mappingStore.GetName(), err.Error()) + fmt.Sprintf("for writerId - %v", writer.writerId))
				}
			}

		}

	case utils.StatusFlapHistory:

		field = datastore.ObjectStatusFlapHistory

	case utils.PolicyFlapHistory:

		fallthrough

	case utils.MetricPolicy:

		fallthrough

	case utils.EventPolicy:

		fallthrough

	case utils.PolicyResult:

		field = utils.PolicySeverity

	case utils.RunbookWorklog:

		dataType = datastore.StringColumn

		field = utils.EventSource

	case utils.Compliance:

		dataType = datastore.IntegerColumn

		field = utils.CompliancePolicyID
	}

	if dataType == datastore.StringColumn {

		writer.batchSize = len(writer.stringFields[field])

	} else {

		writer.batchSize = len(writer.int64Fields[field]) + len(writer.int32Fields[field])
	}

	if writer.dataStoreType == utils.Log && writer.plugin != datastore.LogStatPlugin {

		writer.notifyIndexer()

		writer.compositeStore = datastore.GetStore(datastore.GetStoreName(writer.tick, datastore.EventSearchPlugin, datastore.HorizontalStore), utils.Log, true, true, writer.encoder, writer.tokenizer)

		if writer.compositeStore == nil {

			horizontalWriterLogger.Error(fmt.Sprintf(utils.ErrorAcquireStore, datastore.GetStoreName(writer.tick, datastore.EventSearchPlugin, datastore.HorizontalStore)) + fmt.Sprintf("for writerId - %v", writer.writerId))

			return
		}
	}

	// we send index batch to index writer
	writer.sendIndexBatch(stringFieldTokens, numericFieldTokens)

	writer.writeField(field+utils.OrdinalSuffix, writer.plugin)
}

func (writer *HorizontalWriter) decodeColumns(length int) {

	position := writer.position
	//skipping eventSourceBytes
	writer.length = int(binary.LittleEndian.Uint16(writer.bufferBytes[writer.position : writer.position+2]))

	writer.position += 2

	writer.position += len(writer.bufferBytes[writer.position : writer.position+writer.length])

	//unpack columns and its datatypes
	for writer.position < position+length {

		columnDataType := writer.bufferBytes[writer.position : writer.position+1][0]

		writer.position += 1

		writer.length = int(binary.LittleEndian.Uint32(writer.bufferBytes[writer.position : writer.position+4]))

		writer.position += 4

		column := string(writer.bufferBytes[writer.position : writer.position+writer.length])

		writer.position += len(column)

		if dataType, ok := writer.columns[column]; !ok || dataType != datastore.StringColumn {

			writer.columns[column] = columnDataType
		}

		//we don't need value here, but we need to skip value length here for next record

		if columnDataType == datastore.StringColumn {

			writer.length = int(binary.LittleEndian.Uint32(writer.bufferBytes[writer.position : writer.position+4]))

			writer.position = writer.position + 4 + writer.length

		} else {

			writer.position += 8
		}

	}
}

/*
Here, we unpack the bath, iterate over it, and fill in the missing columns with dummy data.and fill the string fields and numeric fields.
*/
func (writer *HorizontalWriter) processHorizontalBatch(length int, stringFieldTokens map[string]map[string]struct{}, numericFieldTokens map[string]map[int]struct{}) {

	position := writer.position

	eventSourceLength := int(binary.LittleEndian.Uint16(writer.bufferBytes[writer.position : writer.position+2]))

	writer.position += 2

	eventSource := string(writer.bufferBytes[writer.position : writer.position+eventSourceLength])

	writer.position += eventSourceLength

	if len(eventSource) > 0 && (!datastore.IsIndexablePlugin(writer.plugin) || datastore.IsIndexableColumn(writer.plugin, utils.EventSource)) {

		if _, ok := stringFieldTokens[utils.EventSource]; !ok {

			stringFieldTokens[utils.EventSource] = map[string]struct{}{}
		}

		stringFieldTokens[utils.EventSource][eventSource] = struct{}{}
	}

	/*

		position := 0

		columnRow Length = 4 bytes

		position +=4

		{

		columnLength = 2 bytes

		position +=2

		columnValue = columnLength

		position += columLength

		dataType = 1 byte

		}
	*/

	resolvedColumns := map[string]struct{}{}

	var value interface{}

	for writer.position < position+length {

		if utils.GlobalShutdown {

			break
		}

		dataType := writer.bufferBytes[writer.position : writer.position+1][0]

		writer.position++

		// field bytes
		field := writer.readStringColumn()

		if dataType == datastore.IntegerColumn {

			value = int(binary.LittleEndian.Uint64(writer.bufferBytes[writer.position : writer.position+8]))

			writer.position += 8

		} else {

			//value bytes
			value = writer.readStringColumn()
		}

		resolvedColumns[field] = struct{}{}

		if !datastore.IsBlobColumn(field) {

			dataType = writer.getDataType(dataType, field)
		}

		if dataType == datastore.IntegerColumn {

			numericValue := ToINT(value)

			if !datastore.IsIndexablePlugin(writer.plugin) || datastore.IsIndexableColumn(writer.plugin, field) {

				if _, ok := numericFieldTokens[field]; !ok {

					numericFieldTokens[field] = map[int]struct{}{}
				}

				numericFieldTokens[field][numericValue] = struct{}{}
			}

			if _, found := writer.int64Fields[field]; !found {

				if _, found = writer.int32Fields[field]; found {

					if GetDataTypeINT(numericValue) <= Int32 {

						if writer.int32Fields[field+utils.MaxSuffix][0] < int32(numericValue) {

							writer.int32Fields[field+utils.MaxSuffix][0] = int32(numericValue)

						}

						writer.int32Fields[field] = append(writer.int32Fields[field], int32(numericValue))

					} else {

						writer.int64Fields[field] = append(INT32ToINT64Values(writer.int32Fields[field], make([]int64, len(writer.int32Fields[field]))), int64(numericValue))

						writer.int64Fields[field+utils.MaxSuffix] = []int64{int64(numericValue)}

						delete(writer.int32Fields, field)

						delete(writer.int32Fields, field+utils.MaxSuffix)

					}

				} else {

					if GetDataTypeINT(numericValue) <= Int32 {

						writer.int32Fields[field] = []int32{int32(numericValue)}

						writer.int32Fields[field+utils.MaxSuffix] = []int32{int32(numericValue)}

					} else {

						writer.int64Fields[field] = []int64{int64(numericValue)}

						writer.int64Fields[field+utils.MaxSuffix] = []int64{int64(numericValue)}

					}

				}

			} else {

				if writer.int64Fields[field+utils.MaxSuffix][0] < int64(numericValue) {

					writer.int64Fields[field+utils.MaxSuffix][0] = int64(numericValue)

				}

				writer.int64Fields[field] = append(writer.int64Fields[field], int64(numericValue))

			}

		} else if dataType == datastore.StringColumn {

			stringValue := ToString(value)

			writer.stringFields[field] = append(writer.stringFields[field], stringValue)

			writer.probes[field] += len(stringValue)

			if field == utils.Event {

				continue
			}

			isIndexablePlugin := datastore.IsIndexablePlugin(writer.plugin)

			if writer.dataStoreType == utils.Log {

				if !datastore.LogDefaultColumns.Contains(field) {

					if isIndexablePlugin && datastore.IsIndexableColumn(writer.plugin, field) && len(stringValue) > indexFieldMaxLength {

						//tracking the already qualified index fields that now crossing indexfield threshold
						writer.nonIndexableFields[field] = struct{}{}
					}

					// value more than 50 bytes will be discarded from indexable column except URL values
					if !isIndexablePlugin && len(stringValue) > indexFieldMaxLength && !regex.MatchString(stringValue) {

						datastore.AddInvalidIndexableColumn(writer.plugin, field)
					}

				}
			}

			if (!isIndexablePlugin && !datastore.IsInvalidIndexableColumn(writer.plugin, field)) || datastore.IsIndexableColumn(writer.plugin, field) || field == utils.Message {

				if _, ok := stringFieldTokens[field]; !ok {

					stringFieldTokens[field] = map[string]struct{}{}
				}

				stringFieldTokens[field][stringValue] = struct{}{}
			}

			// dynamic blob column probing
			if writer.dataStoreType == utils.Log && !datastore.IsBlobColumn(field) && writer.probes[field] >= utils.MaxBlobBytes {

				datastore.AddBlobColumn(field)
			}

		}
	}

	if len(eventSource) > 0 {

		writer.stringFields[utils.EventSource] = append(writer.stringFields[utils.EventSource], eventSource)
	}

	for column, dataType := range writer.columns {

		if _, ok := resolvedColumns[column]; !ok {

			writer.appendMissingColumn(dataType, column)
		}
	}

	if len(writer.nonIndexableFields) > 0 {

		datastore.AlterIndexableColumns(writer.plugin, writer.nonIndexableFields, utils.Remove)

	}

}

/*
composite txnId is for event history store which contains all log plugins data.
*/
func (writer *HorizontalWriter) setCompositeTxnId(store *Store) error {

	if utils.DebugEnabled() {

		horizontalWriterLogger.Debug(fmt.Sprintf("id:%v, qualifying partition for key: %v,store: %v", writer.writerId, string(writer.keyBytes), store.GetName()))
	}

	writer.txnPartition = store.GetPartition(writer.keyBytes, writer.tokenizer)

	if (writer.dataStoreType == utils.Log) && writer.compositeStore != nil {

		writer.compositeTxnPartition = writer.compositeStore.GetPartition(writer.keyBytes, writer.tokenizer)
	}

	return nil
}

// writes a data of particular part of keys
func (writer *HorizontalWriter) write(store *Store, from, to int, currentPart uint16, shufflePart bool) {

	defer writer.cleanupTxn()

	if utils.DebugEnabled() {

		horizontalWriterLogger.Debug(fmt.Sprintf("id:%v, beginning writing for store: %v, from: %v, to: %v, current-part: %v", writer.writerId, store.GetName(), from, to, currentPart))
	}

	if err := writer.writeFields(store, from, to, currentPart); err != nil {

		writer.cleanupTxn()

		var buffers [][]byte

		buffers, err = store.GetContainKeys([]byte(INT32ToStringValue(writer.tick)), false)

		part := UINT16ToStringValue(currentPart)

		for _, bytes := range buffers {

			if strings.HasSuffix(string(bytes), utils.KeySeparator+part) {

				store.Delete(bytes, writer.encoder, writer.tokenizer)
			}
		}

		horizontalWriterLogger.Error(fmt.Sprintf("error %v occurred while writing event for %v , hence skipping %v records", err, store.GetName(), to-from) + fmt.Sprintf("for writerId %v", writer.writerId))

		return
	}

	copy(writer.txnBufferBytes[writer.txnOffset:], utils.EOTBytes)

	writer.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(writer.txnOffset-4), 0, writer.txnBufferBytes)

	err := writer.commitTxn(store, shufflePart, writer.txnBufferBytes[:writer.txnOffset], writer.txnEntries, writer.txnPartition)

	if err != nil {

		horizontalWriterLogger.Error(fmt.Sprintf("error %v occurred while writing txn for store %v and partition %v", err, store.GetName(), writer.txnPartition))

		return
	}

	if writer.compositeTxnPartition != utils.NotAvailable {

		copy(writer.compositeTxnBufferBytes[writer.compositeTxnOffset:], utils.EOTBytes)

		writer.compositeTxnOffset += len(utils.EOTBytes)

		WriteINT32Value(int32(writer.compositeTxnOffset-4), 0, writer.compositeTxnBufferBytes)

		err = writer.commitTxn(writer.compositeStore, shufflePart, writer.compositeTxnBufferBytes[:writer.compositeTxnOffset], writer.compositeTxnEntries, writer.compositeTxnPartition)

		if err != nil {

			horizontalWriterLogger.Error(fmt.Sprintf("error %v occurred while writing txn for store %v and partition %v", err, writer.compositeStore.GetName(), writer.compositeTxnPartition))

			return
		}
	}

	if utils.DebugEnabled() {

		horizontalWriterLogger.Debug(fmt.Sprintf("id:%v, writing finished for store: %v, from: %v, to: %v, current-part: %v", writer.writerId, store.GetName(), from, to, currentPart))
	}
}

// when data is written for particular transaction, this method will commit the translation for that batch.
func (writer *HorizontalWriter) commitTxn(store *Store, shufflePart bool, buffers []byte, entries map[uint64]utils.TxnEntry, partition int) error {

	err := store.CommitTxn(buffers, entries, writer.encoder, partition)

	if err != nil {

		return err
	}

	if shufflePart {

		if err = store.AddMultipartKey(uint64(writer.tick), 1); err != nil {

			horizontalWriterLogger.Error(fmt.Sprintf("error %v occurred while adding multipart key for writerId %v", err.Error(), writer.writerId))

		}
	}

	return nil
}

func (writer *HorizontalWriter) writeFields(store *Store, startPosition, endPosition int, currentPart uint16) error {

	defer func() {

		if r := recover(); r != nil {

			stackTraceBytes := make([]byte, 1<<20)

			horizontalWriterLogger.Error(fmt.Sprintf("err %v occurred while writing events for plugin : %v, for tick: %v", r, writer.plugin, writer.tick))

			horizontalWriterLogger.Error(fmt.Sprintf("!!!STACK TRACE for horizontal writer %v!!! \n %v", writer.writerId, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}
	}()

	bytePoolIndex := utils.NotAvailable

	var bufferBytes []byte

	var err error

	for field, values := range writer.int32Fields {

		if !strings.HasSuffix(field, utils.MaxSuffix) {

			compositeTxn := false

			if writer.compositeTxnPartition != utils.NotAvailable && field == utils.EventPatternId {

				compositeTxn = true
			}

			values = values[startPosition:endPosition]

			if !datastore.IsIndexableColumn(writer.plugin, field) {

				bytePoolIndex, bufferBytes, err = writer.writeINT32FieldValues(values, field, GetDataTypeINT(int(writer.int32Fields[field+utils.MaxSuffix][0])), store, currentPart)

				if err != nil {

					return errors.New(fmt.Sprintf(utils.ErrorWriteKey, datastore.GetKey(writer.tick, field, currentPart), store.GetName(), err))
				}

				err = writer.writeTxn(field, currentPart, bufferBytes, compositeTxn)

				writer.encoder.MemoryPool.ReleaseBytePool(bytePoolIndex)

				if err != nil {

					return err
				}
			}

			err = writer.writeINT32Ordinals(field, values, store, currentPart, compositeTxn)

			if err != nil {

				return err
			}

		}

	}

	for field, values := range writer.int64Fields {

		if !strings.HasSuffix(field, utils.MaxSuffix) {

			values = values[startPosition:endPosition]

			if !datastore.IsIndexableColumn(writer.plugin, field) {

				bytePoolIndex, bufferBytes, err = writer.writeINT64FieldValues(values, field, GetDataTypeINT(int(writer.int64Fields[field+utils.MaxSuffix][0])), store, currentPart)

				if err != nil {

					return errors.New(fmt.Sprintf(utils.ErrorWriteKey, datastore.GetKey(writer.tick, field, currentPart), store.GetName(), err))
				}

				err = writer.writeTxn(field, currentPart, bufferBytes, false)

				writer.encoder.MemoryPool.ReleaseBytePool(bytePoolIndex)

				if err != nil {

					return err
				}
			}

			err = writer.writeINT64Ordinals(field, values, store, currentPart)

			if err != nil {

				return err
			}
		}

	}

	for field, values := range writer.stringFields {

		values = values[startPosition:endPosition]

		/*
			if message field is same across another plugin or event (a policy message might be written as blob here)
		*/

		compositeTxn := false

		if writer.compositeTxnPartition != utils.NotAvailable && (field == utils.EventSource || field == utils.EventSourceType || field == utils.EventCategory || field == utils.Message || field == utils.Event || field == utils.EventSeverity) {

			compositeTxn = true
		}

		blobColumn := false

		if datastore.IsBlobColumn(field) {

			/*
								currently the blob columns are being written for the event store that is created when the batch is received.

								log plugin is 600000 and log search plugin is 499999

								for instance, if the message field in the log is blob field,
				the blob data will be written in the actual store (600000)
				and the buffer bytes for the blob field are actually the header bytes pointing to where this blob data is
				written in both the actual and log search one.

								so, if there is any case where the blob data needs to be written somewhere else,
				please change the store passed in the below parameters

			*/

			writer.blobEvent.KeyBytes = []byte(datastore.GetKey(writer.tick, field, currentPart))

			writer.blobEvent.ValueBytes = writer.valueBytes

			writer.blobEvent.Values = values

			writer.blobEvent.Encoder = writer.encoder

			writer.blobEvent.Store = store

			writer.blobEvent.Padding = utils.MaxValueBytes

			writer.blobEvent.Tokenizer = writer.tokenizer

			writer.blobEvent.DiskIOEvent = writer.event

			writer.blobEvent.WaitGroup = writer.waitGroup

			writer.blobEvent.Encoding = datastore.GetBlobEncoding(writer.probes[field], writer.batchSize)

			bytePoolIndex, bufferBytes, err = datastore.WriteBlobColumnValues(writer.blobEvent)

			err = writer.writeTxn(field, currentPart, bufferBytes, compositeTxn)

			writer.encoder.MemoryPool.ReleaseBytePool(bytePoolIndex)

			if err != nil {

				return errors.New(fmt.Sprintf(utils.ErrorWriteKey, datastore.GetKey(writer.tick, field, currentPart), store.GetName(), err))
			}

		} else {

			if !datastore.IsIndexableColumn(writer.plugin, field) {

				bytePoolIndex, bufferBytes, err, blobColumn = writer.writeStringFieldValues(values, field, store, currentPart)

				err = writer.writeTxn(field, currentPart, bufferBytes, compositeTxn)

				writer.encoder.MemoryPool.ReleaseBytePool(bytePoolIndex)

				if err != nil {

					return errors.New(fmt.Sprintf(utils.ErrorWriteKey, datastore.GetKey(writer.tick, field, currentPart), store.GetName(), err))
				}
			}

			// column should not be blobcolumn and it's length should not be greater than 100 bytes
			if !blobColumn && !datastore.IsInvalidIndexableColumn(writer.plugin, field) {

				err = writer.writeStringOrdinals(field, values, store, currentPart, compositeTxn)

				if err != nil {

					return err
				}
			}
		}
	}

	return nil
}

func (writer *HorizontalWriter) writeStringFieldValues(values []string, fieldName string, store *Store, currentPart uint16) (int, []byte, error, bool) {

	var stringValues []string

	blobPoolIndex := utils.NotAvailable

	var blobBytes []byte

	keyBytes := []byte(datastore.GetKey(writer.tick, fieldName, currentPart))

	_, valueBytes, err := store.Get(keyBytes, writer.valueBytes, writer.encoder, writer.event, writer.waitGroup, writer.tokenizer, true)

	if err != nil {

		if strings.EqualFold(err.Error(), utils.ErrorTooLarge) {

			blobPoolIndex, blobBytes = writer.decoder.MemoryPool.AcquireBlobPool(utils.NotAvailable)

			_, valueBytes, err = store.Get(keyBytes, blobBytes, writer.encoder, writer.event, writer.waitGroup, writer.tokenizer, true)

		} else if strings.Contains(err.Error(), utils.ErrorIsDeleted) {

			valueBytes = nil

		} else {

			store.Delete(keyBytes, writer.encoder, writer.tokenizer)

			return -1, nil, err, false

		}

	}

	poolIndex := utils.NotAvailable

	if valueBytes != nil && len(valueBytes) > 0 {

		switch dataType := GetDataType(valueBytes[0]); {

		case dataType == Int8:

			index, decodedValues, err := writer.decoder.DecodeINT8Values(GetEncoding(valueBytes[0]), valueBytes[1:], string(keyBytes), store.GetName(), 0)

			if err != nil {

				return writer.handleStringError(blobPoolIndex, err, keyBytes, store.GetName())
			}

			poolIndex, stringValues = writer.decoder.MemoryPool.AcquireStringPool(writer.valueElementSize + len(values))

			INT8ToStringValues(decodedValues, stringValues)

			copy(stringValues[writer.valueElementSize:], values)

			writer.decoder.MemoryPool.ReleaseINT8Pool(index)

			defer writer.decoder.MemoryPool.ReleaseStringPool(poolIndex)

			values = stringValues

			/*
								Acquiring pool size of length(decoded values/previous count) + len(values)
								resetting it to zero will prepend the missing values to zero and then copying the rest
				values to their respective positions
			*/

		case dataType == Int16:

			index, decodedValues, err := writer.decoder.DecodeINT16Values(GetEncoding(valueBytes[0]), valueBytes[1:], string(keyBytes), store.GetName(), 0)

			if err != nil {

				return writer.handleStringError(blobPoolIndex, err, keyBytes, store.GetName())
			}

			/*
								Acquiring pool size of length(decoded values/previous count) + len(values)
								resetting it to zero will prepend the missing values to zero and then copying the rest
				values to their respective positions
			*/

			poolIndex, stringValues = writer.decoder.MemoryPool.AcquireStringPool(writer.valueElementSize + len(values))

			INT16ToStringValues(decodedValues, stringValues)

			copy(stringValues[writer.valueElementSize:], values)

			writer.decoder.MemoryPool.ReleaseINT16Pool(index)

			defer writer.decoder.MemoryPool.ReleaseStringPool(poolIndex)

			values = stringValues

		case dataType == Int24 || dataType == Int32:

			index, decodedValues, err := writer.decoder.DecodeINT32Values(GetEncoding(valueBytes[0]), dataType, valueBytes[1:], string(keyBytes), store.GetName(), 0)

			if err != nil {

				return writer.handleStringError(blobPoolIndex, err, keyBytes, store.GetName())
			}

			/*
								Acquiring pool size of length(decoded values/previous count) + len(values)
								resetting it to zero will prepend the missing values to zero and then copying the rest
				values to their respective positions
			*/

			poolIndex, stringValues = writer.decoder.MemoryPool.AcquireStringPool(writer.valueElementSize + len(values))

			INT32ToStringValues(decodedValues, stringValues)

			copy(stringValues[writer.valueElementSize:], values)

			writer.decoder.MemoryPool.ReleaseINT32Pool(index)

			defer writer.decoder.MemoryPool.ReleaseStringPool(poolIndex)

			values = stringValues

		case dataType == Int40 || dataType == Int48 || dataType == Int56 || dataType == Int64:

			index, decodedValues, err := writer.decoder.DecodeINT64Values(GetEncoding(valueBytes[0]), dataType, valueBytes[1:], string(keyBytes), store.GetName(), 0)

			if err != nil {

				return writer.handleStringError(blobPoolIndex, err, keyBytes, store.GetName())
			}

			/*
								Acquiring pool size of length(decoded values/previous count) + len(values)
								resetting it to zero will prepend the missing values to zero and then copying the rest
				values to their respective positions
			*/

			poolIndex, stringValues = writer.decoder.MemoryPool.AcquireStringPool(writer.valueElementSize + len(values))

			INT64ToStringValues(decodedValues, stringValues)

			copy(stringValues[writer.valueElementSize:], values)

			writer.decoder.MemoryPool.ReleaseINT64Pool(index)

			defer writer.decoder.MemoryPool.ReleaseStringPool(poolIndex)

			values = stringValues

		case dataType == String:

			index, decodedValues, err := writer.decoder.DecodeStringValues(GetEncoding(valueBytes[0]), valueBytes[1:], string(keyBytes), store.GetName(), 0)

			if err != nil {

				return writer.handleStringError(blobPoolIndex, err, keyBytes, store.GetName())

			}

			poolIndex, stringValues = writer.decoder.MemoryPool.AcquireStringPool(writer.valueElementSize + len(values))

			copy(stringValues, decodedValues)

			copy(stringValues[writer.valueElementSize:], values)

			writer.decoder.MemoryPool.ReleaseStringPool(index)

			defer writer.decoder.MemoryPool.ReleaseStringPool(poolIndex)

			dataType = String

			values = stringValues

		default:

			if blobPoolIndex != utils.NotAvailable {

				writer.encoder.MemoryPool.ReleaseBytePool(blobPoolIndex)

				blobPoolIndex = utils.NotAvailable
			}

			return -1, nil, errors.New(fmt.Sprintf(utils.ErrorUnknownDataType, keyBytes, store.GetName())), false
		}
	} else if writer.valueElementSize > 0 {

		/*
						if the previous count is available and this block is executed, it means that this is a new field
			arrived here to be inserted which was not along with the previous fields written for this tick for this
			event type hence need to prepend missing values with 0
		*/

		poolIndex, stringValues = writer.decoder.MemoryPool.AcquireStringPool(writer.valueElementSize + len(values))

		copy(stringValues[writer.valueElementSize:], values)

		defer writer.decoder.MemoryPool.ReleaseStringPool(poolIndex)

		values = stringValues

	}

	if blobPoolIndex != utils.NotAvailable {

		writer.encoder.MemoryPool.ReleaseBytePool(blobPoolIndex)

		blobPoolIndex = utils.NotAvailable
	}

	if writer.dataStoreType == utils.Log && utils.GetStringSliceSize(values) >= utils.MaxBlobBytes {

		datastore.AddBlobColumn(fieldName)

		writer.blobEvent.KeyBytes = []byte(datastore.GetKey(writer.tick, fieldName, currentPart))

		writer.blobEvent.ValueBytes = writer.valueBytes

		writer.blobEvent.Values = values

		writer.blobEvent.Encoder = writer.encoder

		writer.blobEvent.Store = store

		writer.blobEvent.Padding = utils.MaxValueBytes

		writer.blobEvent.Tokenizer = writer.tokenizer

		writer.blobEvent.DiskIOEvent = writer.event

		writer.blobEvent.WaitGroup = writer.waitGroup

		writer.blobEvent.Encoding = datastore.GetBlobEncoding(writer.probes[fieldName], writer.batchSize)

		bytePoolIndex, bufferBytes, err := datastore.WriteBlobColumnValues(writer.blobEvent)

		return bytePoolIndex, bufferBytes, err, true
	}

	bytePoolIndex, bufferBytes, err := writer.encoder.EncodeStringValues(values, datastore.GetColumnEncoder(fieldName), utils.MaxValueBytes, string(keyBytes))

	return bytePoolIndex, bufferBytes, err, false

}

func (writer *HorizontalWriter) writeINT32FieldValues(values []int32, fieldName string, dataType DataType, store *Store, currentPart uint16) (int, []byte, error) {

	var int64Values []int64

	var stringValues []string

	var keyBytes []byte

	keyBytes = []byte(datastore.GetKey(writer.tick, fieldName, currentPart))

	_, valueBytes, err := store.Get(keyBytes, writer.valueBytes, writer.encoder, writer.event, writer.waitGroup, writer.tokenizer, true)

	blobPoolIndex := utils.NotAvailable

	if err != nil {

		if strings.EqualFold(err.Error(), utils.ErrorTooLarge) {

			var blobBytes []byte

			blobPoolIndex, blobBytes = writer.decoder.MemoryPool.AcquireBlobPool(utils.NotAvailable)

			_, valueBytes, err = store.Get(keyBytes, blobBytes, writer.encoder, writer.event, writer.waitGroup, writer.tokenizer, true)

		} else if strings.Contains(err.Error(), utils.ErrorIsDeleted) {

			valueBytes = nil

		} else {

			store.Delete(keyBytes, writer.encoder, writer.tokenizer)

			return -1, nil, err
		}
	}

	poolIndex := utils.NotAvailable

	if valueBytes != nil && len(valueBytes) > 0 {

		switch currentDataType := GetDataType(valueBytes[0]); {

		case currentDataType == Int8:

			index, decodedValues, err := writer.decoder.DecodeINT8Values(GetEncoding(valueBytes[0]), valueBytes[1:], string(keyBytes), store.GetName(), 0)

			if err != nil {

				return writer.handleNumericError(blobPoolIndex, err, keyBytes, store.GetName())
			}

			/*
								Acquiring pool size of length(decoded values/previous count) + len(values)
								resetting it to zero will prepend the missing values to zero and then copying the rest
				values to their respective positions
			*/

			poolIndex, int32Values := writer.decoder.MemoryPool.AcquireINT32Pool(writer.valueElementSize + len(values))

			writer.decoder.MemoryPool.ResetINT32Pool(poolIndex, writer.valueElementSize, 0)

			int32Values = INT8ToINT32Values(decodedValues, int32Values)

			copy(int32Values[writer.valueElementSize:], values)

			defer writer.decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

			values = int32Values

			writer.decoder.MemoryPool.ReleaseINT8Pool(index)

		case currentDataType == Int16:

			index, decodedValues, err := writer.decoder.DecodeINT16Values(GetEncoding(valueBytes[0]), valueBytes[1:], string(keyBytes), store.GetName(), 0)

			if err != nil {

				return writer.handleNumericError(blobPoolIndex, err, keyBytes, store.GetName())
			}

			/*
								Acquiring pool size of length(decoded values/previous count) + len(values)
								resetting it to zero will prepend the missing values to zero and then copying the rest
				values to their respective positions
			*/

			poolIndex, int32Values := writer.decoder.MemoryPool.AcquireINT32Pool(writer.valueElementSize + len(values))

			writer.decoder.MemoryPool.ResetINT32Pool(poolIndex, writer.valueElementSize, 0)

			int32Values = INT16ToINT32Values(decodedValues, int32Values)

			copy(int32Values[writer.valueElementSize:], values)

			defer writer.decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

			values = int32Values

			if currentDataType > dataType {

				dataType = currentDataType
			}

			writer.decoder.MemoryPool.ReleaseINT16Pool(index)

		case currentDataType == Int24 || currentDataType == Int32:

			index, decodedValues, err := writer.decoder.DecodeINT32Values(GetEncoding(valueBytes[0]), currentDataType, valueBytes[1:], string(keyBytes), store.GetName(), 0)

			if err != nil {

				return writer.handleNumericError(blobPoolIndex, err, keyBytes, store.GetName())
			}

			poolIndex, int32Values := writer.decoder.MemoryPool.AcquireINT32Pool(writer.valueElementSize + len(values))

			writer.decoder.MemoryPool.ResetINT32Pool(poolIndex, writer.valueElementSize, 0)

			copy(int32Values, decodedValues)

			copy(int32Values[writer.valueElementSize:], values)

			values = int32Values

			defer writer.decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

			writer.decoder.MemoryPool.ReleaseINT32Pool(index)

			if currentDataType > dataType {

				dataType = currentDataType
			}

		case currentDataType == Int40 || currentDataType == Int48 || currentDataType == Int56 || currentDataType == Int64:

			index, decodedValues, err := writer.decoder.DecodeINT64Values(GetEncoding(valueBytes[0]), currentDataType, valueBytes[1:], string(keyBytes), store.GetName(), 0)

			if err != nil {

				return writer.handleNumericError(blobPoolIndex, err, keyBytes, store.GetName())
			}

			poolIndex, int64Values = writer.decoder.MemoryPool.AcquireINT64Pool(writer.valueElementSize + len(values))

			writer.decoder.MemoryPool.ResetINT64Pool(poolIndex, writer.valueElementSize, 0)

			copy(int64Values, decodedValues)

			INT32ToINT64Values(values, int64Values[writer.valueElementSize:])

			writer.decoder.MemoryPool.ReleaseINT64Pool(index)

			defer writer.decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

			dataType = currentDataType

		case currentDataType == String:

			index, decodedValues, err := writer.decoder.DecodeStringValues(GetEncoding(valueBytes[0]), valueBytes[1:], string(keyBytes), store.GetName(), 0)

			if err != nil {

				return writer.handleNumericError(blobPoolIndex, err, keyBytes, store.GetName())
			}

			poolIndex, stringValues = writer.decoder.MemoryPool.AcquireStringPool(writer.valueElementSize + len(values))

			copy(stringValues, decodedValues)

			INT32ToStringValues(values, stringValues[writer.valueElementSize:])

			writer.decoder.MemoryPool.ReleaseStringPool(index)

			defer writer.decoder.MemoryPool.ReleaseStringPool(poolIndex)

			dataType = currentDataType

		default:

			if blobPoolIndex != utils.NotAvailable {

				writer.encoder.MemoryPool.ReleaseBytePool(blobPoolIndex)

				blobPoolIndex = utils.NotAvailable
			}

			return -1, nil, errors.New(fmt.Sprintf(utils.ErrorUnknownDataType, keyBytes, store.GetName()))
		}

	} else if writer.valueElementSize > 0 {

		switch {

		case dataType == Int8:

			fallthrough

		case dataType == Int16:

			fallthrough

		case dataType == Int24 || dataType == Int32:

			poolIndex, int32Values := writer.decoder.MemoryPool.AcquireINT32Pool(writer.valueElementSize + len(values))

			writer.decoder.MemoryPool.ResetINT32Pool(poolIndex, writer.valueElementSize, 0) //pre-pend 0 value for the missing count

			for i := 0; i < len(values); i++ {

				int32Values[i+writer.valueElementSize] = values[i]
			}

			values = int32Values

			defer writer.decoder.MemoryPool.ReleaseINT32Pool(poolIndex)
		}

	}

	if blobPoolIndex != utils.NotAvailable {

		writer.encoder.MemoryPool.ReleaseBytePool(blobPoolIndex)

		blobPoolIndex = utils.NotAvailable
	}

	switch {

	case dataType == Int8 || dataType == Int16 || dataType == Int24 || dataType == Int32:

		return writer.encoder.EncodeINT32Values(values, datastore.GetColumnEncoder(fieldName), dataType, GetDataTypeBits(dataType), utils.MaxValueBytes)

	case dataType == Int40 || dataType == Int48 || dataType == Int56 || dataType == Int64:

		return writer.encoder.EncodeINT64Values(int64Values, datastore.GetColumnEncoder(fieldName), dataType, utils.MaxValueBytes)

	case dataType == String:

		return writer.encoder.EncodeStringValues(stringValues, datastore.GetColumnEncoder(fieldName), utils.MaxValueBytes, string(keyBytes))

	}

	return -1, nil, nil

}

func (writer *HorizontalWriter) writeINT64FieldValues(values []int64, fieldName string, dataType DataType, store *Store, currentPart uint16) (int, []byte, error) {

	var err error

	var stringValues []string

	var int64Values []int64

	var keyBytes []byte

	keyBytes = []byte(datastore.GetKey(writer.tick, fieldName, currentPart))

	poolIndex := utils.NotAvailable

	_, valueBytes, err := store.Get(keyBytes, writer.valueBytes, writer.encoder, writer.event, writer.waitGroup, writer.tokenizer, true)

	blobPoolIndex := utils.NotAvailable

	if err != nil {

		if strings.EqualFold(err.Error(), utils.ErrorTooLarge) {

			var blobBytes []byte

			blobPoolIndex, blobBytes = writer.decoder.MemoryPool.AcquireBlobPool(utils.NotAvailable)

			_, valueBytes, err = store.Get(keyBytes, blobBytes, writer.encoder, writer.event, writer.waitGroup, writer.tokenizer, true)
		} else if strings.Contains(err.Error(), utils.ErrorIsDeleted) {

			valueBytes = nil

		} else {

			store.Delete(keyBytes, writer.encoder, writer.tokenizer)

			return -1, nil, err
		}
	}

	if valueBytes != nil && len(valueBytes) > 0 {

		switch currentDataType := GetDataType(valueBytes[0]); {

		case currentDataType == Int8:

			index, decodedValues, err := writer.decoder.DecodeINT8Values(GetEncoding(valueBytes[0]), valueBytes[1:], string(keyBytes), store.GetName(), 0)

			if err != nil {

				return writer.handleNumericError(blobPoolIndex, err, keyBytes, store.GetName())
			}

			poolIndex, int64Values = writer.decoder.MemoryPool.AcquireINT64Pool(writer.valueElementSize + len(values))

			writer.decoder.MemoryPool.ResetINT64Pool(poolIndex, writer.valueElementSize, 0)

			int64Values = INT8ToINT64Values(decodedValues, int64Values)

			copy(int64Values[writer.valueElementSize:], values)

			defer writer.decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

			writer.decoder.MemoryPool.ReleaseINT8Pool(index)

			values = int64Values

		case currentDataType == Int16:

			index, decodedValues, err := writer.decoder.DecodeINT16Values(GetEncoding(valueBytes[0]), valueBytes[1:], string(keyBytes), store.GetName(), 0)

			if err != nil {

				return writer.handleNumericError(blobPoolIndex, err, keyBytes, store.GetName())
			}

			poolIndex, int64Values = writer.decoder.MemoryPool.AcquireINT64Pool(writer.valueElementSize + len(values))

			writer.decoder.MemoryPool.ResetINT64Pool(poolIndex, writer.valueElementSize, 0)

			int64Values = INT16ToINT64Values(decodedValues, int64Values)

			copy(int64Values[writer.valueElementSize:], values)

			defer writer.decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

			writer.decoder.MemoryPool.ReleaseINT16Pool(index)

			values = int64Values

		case currentDataType == Int24 || currentDataType == Int32:

			index, decodedValues, err := writer.decoder.DecodeINT32Values(GetEncoding(valueBytes[0]), currentDataType, valueBytes[1:], string(keyBytes), store.GetName(), 0)

			if err != nil {

				return writer.handleNumericError(blobPoolIndex, err, keyBytes, store.GetName())
			}

			poolIndex, int64Values = writer.decoder.MemoryPool.AcquireINT64Pool(writer.valueElementSize + len(values))

			writer.decoder.MemoryPool.ResetINT64Pool(poolIndex, writer.valueElementSize, 0)

			int64Values = INT32ToINT64Values(decodedValues, int64Values)

			copy(int64Values[writer.valueElementSize:], values)

			defer writer.decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

			writer.decoder.MemoryPool.ReleaseINT32Pool(index)

			values = int64Values

		case currentDataType == Int40 || currentDataType == Int48 || currentDataType == Int56 || currentDataType == Int64:

			index, decodedValues, err := writer.decoder.DecodeINT64Values(GetEncoding(valueBytes[0]), currentDataType, valueBytes[1:], string(keyBytes), store.GetName(), 0)

			if err != nil {

				return writer.handleNumericError(blobPoolIndex, err, keyBytes, store.GetName())
			}

			poolIndex, int64Values = writer.decoder.MemoryPool.AcquireINT64Pool(writer.valueElementSize + len(values))

			writer.decoder.MemoryPool.ResetINT64Pool(poolIndex, writer.valueElementSize, 0)

			copy(int64Values, decodedValues)

			copy(int64Values[writer.valueElementSize:], values)

			writer.decoder.MemoryPool.ReleaseINT64Pool(index)

			defer writer.decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

			values = int64Values

			if currentDataType > dataType {

				dataType = currentDataType
			}

		case currentDataType == String:

			index, decodedValues, err := writer.decoder.DecodeStringValues(GetEncoding(valueBytes[0]), valueBytes[1:], string(keyBytes), store.GetName(), 0)

			if err != nil {

				return writer.handleNumericError(blobPoolIndex, err, keyBytes, store.GetName())

			}

			poolIndex, stringValues = writer.decoder.MemoryPool.AcquireStringPool(writer.valueElementSize + len(values))

			copy(stringValues, decodedValues)

			INT64ToStringValues(values, stringValues[writer.valueElementSize:])

			writer.decoder.MemoryPool.ReleaseStringPool(index)

			defer writer.decoder.MemoryPool.ReleaseStringPool(poolIndex)

			dataType = String

		default:

			if blobPoolIndex != utils.NotAvailable {

				writer.encoder.MemoryPool.ReleaseBytePool(blobPoolIndex)

				blobPoolIndex = utils.NotAvailable
			}

			return -1, nil, errors.New(fmt.Sprintf(utils.ErrorUnknownDataType, keyBytes, store.GetName()))
		}

	} else if writer.valueElementSize > 0 {

		switch {

		case dataType == Int40 || dataType == Int48 || dataType == Int56 || dataType == Int64:

			poolIndex, int64Values = writer.decoder.MemoryPool.AcquireINT64Pool(writer.valueElementSize + len(values))

			writer.decoder.MemoryPool.ResetINT64Pool(poolIndex, writer.valueElementSize, 0)

			copy(int64Values[writer.valueElementSize:], values)

			defer writer.decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

			values = int64Values
		}

	}

	if blobPoolIndex != utils.NotAvailable {

		writer.encoder.MemoryPool.ReleaseBytePool(blobPoolIndex)

		blobPoolIndex = utils.NotAvailable
	}

	switch {

	case dataType == Int40 || dataType == Int48 || dataType == Int56 || dataType == Int64:

		return writer.encoder.EncodeINT64Values(values, datastore.GetColumnEncoder(fieldName), dataType, utils.MaxValueBytes)

	case dataType == String:

		return writer.encoder.EncodeStringValues(stringValues, datastore.GetColumnEncoder(fieldName), utils.MaxValueBytes, string(keyBytes))
	}

	return -1, nil, err

}

/*
While in the case of logs, to identify the indexing columns, we notify the indexer for specific batch numbers,
and after concluding the columns for logs, we stop notifying the index job.
*/
func (writer *HorizontalWriter) notifyIndexer() {

	if datastore.IsIndexablePlugin(writer.plugin) || utils.IndexJobRequests == nil {

		return
	}

	request := utils.MotadataMap{}

	columns := make(map[string]interface{})

	for field := range writer.int64Fields {

		if strings.HasSuffix(field, utils.MaxSuffix) || datastore.IsNonIndexableColumn(field) {

			continue
		}

		columns[field] = utils.Empty
	}

	for field := range writer.int32Fields {

		if strings.HasSuffix(field, utils.MaxSuffix) || datastore.IsNonIndexableColumn(field) {

			continue
		}

		columns[field] = utils.Empty
	}

	for field := range writer.stringFields {

		if datastore.IsNonIndexableColumn(field) {

			continue
		}

		columns[field] = utils.Empty
	}

	if len(columns) > 0 {

		request[utils.Columns] = columns

		request[utils.Plugin] = writer.plugin

		request[utils.BatchSize] = writer.batchSize

		utils.IndexJobRequests <- request
	}
}

// to get a highest datatype from the available fields
func (writer *HorizontalWriter) getDataType(dataType byte, field string) byte {

	switch dataType {

	case datastore.IntegerColumn:

		if _, found := writer.stringFields[field]; found {

			return datastore.StringColumn

		}

		return datastore.IntegerColumn

	default:

		if values, found := writer.int32Fields[field]; found {

			delete(writer.int32Fields, field)

			delete(writer.int32Fields, field+utils.MaxSuffix)

			writer.stringFields[field] = append(writer.stringFields[field], INT32ToStringValues(values, make([]string, len(values)))...)

		} else if values, found := writer.int64Fields[field]; found {

			delete(writer.int64Fields, field)

			delete(writer.int64Fields, field+utils.MaxSuffix)

			writer.stringFields[field] = append(writer.stringFields[field], INT64ToStringValues(values, make([]string, len(values)))...)

		}

		return datastore.StringColumn

	}

}

func (writer *HorizontalWriter) writeField(field, name string) {

	if utils.DebugEnabled() {

		horizontalWriterLogger.Debug(fmt.Sprintf("id:%v, event type: %v , plugin: %v , tick %v", writer.writerId, datastore.GetDatastoreType(writer.dataStoreType), writer.plugin, utils.SecondsToUnix(writer.tick)))

	}

	store := datastore.GetStore(datastore.GetStoreName(writer.tick, name, datastore.HorizontalStore), writer.dataStoreType, true, true, writer.encoder, writer.tokenizer)

	if store == nil {

		horizontalWriterLogger.Error(fmt.Sprintf(utils.ErrorAcquireStore, datastore.GetStoreName(writer.tick, name, datastore.HorizontalStore)) + fmt.Sprintf(MessageWriterId, writer.writerId))

		return
	}

	currentPart := store.GetMaxPart(uint64(writer.tick))

	valueElementSize := 0

	writer.keyBytes = []byte(datastore.GetKey(writer.tick, field, currentPart)) // assuming field is always int32 datatype

	_, valueBytes, err := store.Get(writer.keyBytes, writer.valueBytes, writer.encoder, writer.event, writer.waitGroup, writer.tokenizer, true)

	if err != nil {

		store.Delete(writer.keyBytes, writer.encoder, writer.tokenizer)

		var buffers [][]byte

		buffers, err = store.GetContainKeys([]byte(INT32ToStringValue(writer.tick)), false)

		part := UINT16ToStringValue(currentPart)

		for _, bytes := range buffers {

			if strings.HasSuffix(string(bytes), utils.KeySeparator+part) {

				store.Delete(bytes, writer.encoder, writer.tokenizer)
			}
		}
	}

	if valueBytes != nil && len(valueBytes) > 0 {

		poolIndex, values, err := writer.decoder.DecodeINT32Values(GetEncoding(valueBytes[0]), GetDataType(valueBytes[0]), valueBytes[1:], string(writer.keyBytes), store.GetName(), 0)

		if err != nil {

			if poolIndex != utils.NotAvailable {

				writer.decoder.MemoryPool.ReleaseINT32Pool(poolIndex)
			}

			store.Delete(writer.keyBytes, writer.encoder, writer.tokenizer)

			horizontalWriterLogger.Error(fmt.Sprintf(utils.ErrorDecodeValues, string(writer.keyBytes), store.GetName(), err.Error()) + fmt.Sprintf(MessageWriterId, writer.writerId))

			return
		}

		valueElementSize = len(values)

		writer.decoder.MemoryPool.ReleaseINT32Pool(poolIndex)
	}

	// shuffle part is for when batch size is greater than pool length,
	// we need to write it in next part
	shufflePart := false

	parts := 0 //parts not including the current part

	if writer.batchSize > writer.overflowPoolLength { //batch size is greater so need to divide

		if valueElementSize > 0 {

			shufflePart = true
		}

		parts = int(math.Ceil(float64(writer.batchSize) / float64(writer.overflowPoolLength)))

	} else if valueElementSize+writer.batchSize > writer.overflowPoolLength {

		shufflePart = true

		parts = 1
	}

	if utils.DebugEnabled() {

		horizontalWriterLogger.Debug(fmt.Sprintf("id:%v, overflow threshold length: %v,current ongoing part: %v ,previous count: %v,qualified parts: %v", writer.writerId, writer.overflowPoolLength, currentPart, valueElementSize, parts))

	}

	from := 0

	to := writer.batchSize

	if to > writer.overflowPoolLength {

		to = writer.overflowPoolLength
	}

	if parts > 0 {

		for i := 0; i < parts; i++ {

			if i == parts-1 {

				to = writer.batchSize
			}

			if shufflePart {

				/*
										In case of overflow,
					The flag change part is for writing in part 0 or not
					when there is something written in part 0, the change part will be true, else false.
				*/

				currentPart++

				valueElementSize = 0

			}

			if err := writer.setCompositeTxnId(store); err != nil {

				horizontalWriterLogger.Error(fmt.Sprintf("error %v occurred while generating txn id for %v , hence skipping %v records for writerId %v", err, store.GetName(), to-from, writer.writerId))

			} else {

				writer.valueElementSize = valueElementSize

				writer.write(store, from, to, currentPart, shufflePart)

			}

			shufflePart = true

			valueElementSize = 0

			from = to

			to += writer.overflowPoolLength

		}

	} else {

		if err := writer.setCompositeTxnId(store); err != nil {

			horizontalWriterLogger.Error(fmt.Sprintf("error %v occurred while generating txn id for %v , hence skipping %v records for writerId %v", err, store.GetName(), to-from, writer.writerId))

		} else {

			writer.valueElementSize = valueElementSize

			writer.write(store, from, to, currentPart, shufflePart)

		}
	}
}

func (writer *HorizontalWriter) writeTxn(field string, currentPart uint16, bufferBytes []byte, compositeTxn bool) (err error) {

	keyBytes := []byte(datastore.GetKey(writer.tick, field, currentPart))

	if size := len(bufferBytes) + 4 + len(utils.EOTBytes) + len(keyBytes) + writer.txnOffset; size > len(writer.txnBufferBytes) {

		horizontalWriterLogger.Info(fmt.Sprintf("remapping annonymous txn buffers with current length %v and required size %v", len(writer.txnBufferBytes), size))

		writer.txnBufferBytes = utils.RemapBytes(writer.txnBufferBytes, size)
	}

	copy(bufferBytes, utils.CheckSumV1Bytes) // add checksum

	WriteINT32Value(int32(len(bufferBytes)-utils.MaxValueBytes), 4, bufferBytes) //add length

	WriteINT32Value(int32(len(keyBytes)), 0, writer.int32Bytes) //calculate length of key bytes

	copy(writer.txnBufferBytes[writer.txnOffset:], writer.int32Bytes) //add key length bytes

	writer.txnOffset += 4

	copy(writer.txnBufferBytes[writer.txnOffset:], keyBytes)

	writer.txnOffset += len(keyBytes)

	offset := writer.txnOffset

	copy(writer.txnBufferBytes[writer.txnOffset:], bufferBytes)

	writer.txnOffset += len(bufferBytes)

	writer.txnEntries[utils.GetHash64(keyBytes)] = utils.TxnEntry{Length: len(bufferBytes), Offset: offset}

	if compositeTxn {

		keyBytes = []byte(datastore.GetKey(writer.tick, writer.plugin+utils.KeySeparator+field, currentPart))

		if size := len(bufferBytes) + 4 + len(keyBytes) + writer.compositeTxnOffset; size > len(writer.compositeTxnBufferBytes) {

			horizontalWriterLogger.Info(fmt.Sprintf("remapping annonymous txn buffers with current length %v and required size %v", len(writer.compositeTxnBufferBytes), size))

			writer.compositeTxnBufferBytes = utils.RemapBytes(writer.compositeTxnBufferBytes, size)
		}

		WriteINT32Value(int32(len(keyBytes)), 0, writer.int32Bytes) //calculate length of key bytes

		copy(writer.compositeTxnBufferBytes[writer.compositeTxnOffset:], writer.int32Bytes) //add key length bytes

		writer.compositeTxnOffset += 4

		copy(writer.compositeTxnBufferBytes[writer.compositeTxnOffset:], keyBytes)

		writer.compositeTxnOffset += len(keyBytes)

		offset = writer.compositeTxnOffset

		copy(writer.compositeTxnBufferBytes[writer.compositeTxnOffset:], bufferBytes)

		writer.compositeTxnOffset += len(bufferBytes)

		writer.compositeTxnEntries[utils.GetHash64(keyBytes)] = utils.TxnEntry{Length: len(bufferBytes), Offset: offset}

	}

	return
}

func (writer *HorizontalWriter) writeStringOrdinals(field string, values []string, store *Store, currentPart uint16, compositeTxn bool) error {

	if datastore.IsIndexableColumn(writer.plugin, field) || (!(datastore.IsIndexablePlugin(writer.plugin)) && writer.dataStoreType == utils.Log) {

		mappingStore := datastore.GetStore(field+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, writer.encoder, writer.tokenizer)

		poolIndex, stringValues := writer.encoder.MemoryPool.AcquireStringPool(len(values))

		copy(stringValues, values)

		err, ordinalPoolIndex, ordinals := mappingStore.MapStringValues(poolIndex, writer.encoder, len(stringValues))

		if err != nil && !strings.Contains(err.Error(), utils.ErrorIteratorDone) {

			writer.encoder.MemoryPool.ReleaseStringPool(poolIndex)

			return errors.New(fmt.Sprintf(utils.ErrorGenerateNumericMappings, datastore.GetKey(writer.tick, field, currentPart), store.GetName(), err))

		}

		field += utils.OrdinalSuffix

		bufferIndex, bufferBytes, err := writer.writeINT32FieldValues(ordinals, field, Int32, store, currentPart)

		writer.encoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)

		writer.encoder.MemoryPool.ReleaseStringPool(poolIndex)

		if err != nil {

			writer.encoder.MemoryPool.ReleaseBytePool(bufferIndex)

			return errors.New(fmt.Sprintf(utils.ErrorWriteKey, datastore.GetKey(writer.tick, field, currentPart), store.GetName(), err))
		}

		err = writer.writeTxn(field, currentPart, bufferBytes, compositeTxn)

		writer.encoder.MemoryPool.ReleaseBytePool(bufferIndex)

		if err != nil {

			return errors.New(fmt.Sprintf(utils.ErrorWriteKey, datastore.GetKey(writer.tick, field, currentPart), store.GetName(), err))
		}

	}

	return nil

}

func (writer *HorizontalWriter) writeINT64Ordinals(field string, values []int64, store *Store, currentPart uint16) error {

	if datastore.IsIndexableColumn(writer.plugin, field) || (!(datastore.IsIndexablePlugin(writer.plugin)) && writer.dataStoreType == utils.Log) {

		mappingStore := datastore.GetStore(field+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, writer.encoder, writer.tokenizer)

		poolIndex, int64Values := writer.encoder.MemoryPool.AcquireINT64Pool(len(values))

		copy(int64Values, values)

		err, ordinalPoolIndex, ordinals := mappingStore.MapNumericValues(poolIndex, writer.encoder, len(int64Values))

		if err != nil && !strings.Contains(err.Error(), utils.ErrorIteratorDone) {

			writer.encoder.MemoryPool.ReleaseINT64Pool(poolIndex)

			return errors.New(fmt.Sprintf(utils.ErrorGenerateNumericMappings, datastore.GetKey(writer.tick, field, currentPart), store.GetName(), err))

		}

		field += utils.OrdinalSuffix

		bufferIndex, bufferBytes, err := writer.writeINT32FieldValues(ordinals, field, Int32, store, currentPart)

		writer.encoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)

		writer.encoder.MemoryPool.ReleaseINT64Pool(poolIndex)

		if err != nil {

			writer.encoder.MemoryPool.ReleaseBytePool(bufferIndex)

			return errors.New(fmt.Sprintf(utils.ErrorWriteKey, datastore.GetKey(writer.tick, field, currentPart), store.GetName(), err))
		}

		err = writer.writeTxn(field, currentPart, bufferBytes, false)

		writer.encoder.MemoryPool.ReleaseBytePool(bufferIndex)

		if err != nil {

			return errors.New(fmt.Sprintf(utils.ErrorWriteKey, datastore.GetKey(writer.tick, field, currentPart), store.GetName(), err))
		}

	}

	return nil

}

func (writer *HorizontalWriter) writeINT32Ordinals(field string, values []int32, store *Store, currentPart uint16, compositeTxn bool) error {

	if datastore.IsIndexableColumn(writer.plugin, field) || (!(datastore.IsIndexablePlugin(writer.plugin)) && writer.dataStoreType == utils.Log) {

		mappingStore := datastore.GetStore(field+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, writer.encoder, writer.tokenizer)

		poolIndex, int64Values := writer.encoder.MemoryPool.AcquireINT64Pool(len(values))

		INT32ToINT64Values(values, int64Values)

		err, ordinalPoolIndex, ordinals := mappingStore.MapNumericValues(poolIndex, writer.encoder, len(int64Values))

		if err != nil && !strings.Contains(err.Error(), utils.ErrorIteratorDone) {

			writer.encoder.MemoryPool.ReleaseINT64Pool(poolIndex)

			return errors.New(fmt.Sprintf(utils.ErrorGenerateNumericMappings, datastore.GetKey(writer.tick, field, currentPart), store.GetName(), err))

		}

		field += utils.OrdinalSuffix

		bufferIndex, bufferBytes, err := writer.writeINT32FieldValues(ordinals, field, Int32, store, currentPart)

		writer.encoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)

		writer.encoder.MemoryPool.ReleaseINT64Pool(poolIndex)

		if err != nil {

			writer.encoder.MemoryPool.ReleaseBytePool(bufferIndex)

			return errors.New(fmt.Sprintf(utils.ErrorWriteKey, datastore.GetKey(writer.tick, field, currentPart), store.GetName(), err))
		}

		err = writer.writeTxn(field, currentPart, bufferBytes, compositeTxn)

		writer.encoder.MemoryPool.ReleaseBytePool(bufferIndex)

		if err != nil {

			return errors.New(fmt.Sprintf(utils.ErrorWriteKey, datastore.GetKey(writer.tick, field, currentPart), store.GetName(), err))
		}

	}

	return nil

}

/*
we send batch to index writer with lookup event false,while strings and numeric tokens with lookup event true.
and if store is for log then we send one extra event for composite store also.
*/
func (writer *HorizontalWriter) sendIndexBatch(stringFieldTokens map[string]map[string]struct{}, numericFieldTokens map[string]map[int]struct{}) {

	utils.IndexWriterRequests[utils.GetFastModN(utils.GetHash64([]byte(writer.plugin)), utils.IndexWriters)] <- utils.IndexEvent{

		Plugin: writer.plugin, StoreType: writer.dataStoreType, Tick: writer.tick, LookupEvent: false,
	}

	if writer.dataStoreType == utils.Log {

		utils.IndexWriterRequests[utils.GetFastModN(utils.GetHash64([]byte(datastore.EventSearchPlugin)), utils.IndexWriters)] <- utils.IndexEvent{

			Plugin: datastore.EventSearchPlugin, StoreType: writer.dataStoreType, Tick: writer.tick, LookupEvent: false,
		}
	}

	for field, context := range stringFieldTokens {

		if writer.dataStoreType == utils.Log && datastore.IsEventMetadataField(field) {

			utils.IndexWriterRequests[utils.GetFastModN(utils.GetHash64([]byte(datastore.EventSearchPlugin+field)), utils.IndexWriters)] <- utils.IndexEvent{

				Plugin: datastore.EventSearchPlugin, StoreType: writer.dataStoreType, Tick: writer.tick, LookupEvent: true, Column: field, StringContext: context, NumericContext: nil,
			}
		} else {

			utils.IndexWriterRequests[utils.GetFastModN(utils.GetHash64([]byte(writer.plugin+field)), utils.IndexWriters)] <- utils.IndexEvent{

				Plugin: writer.plugin, StoreType: writer.dataStoreType, Tick: writer.tick, LookupEvent: true, Column: field, StringContext: context, NumericContext: nil,
			}
		}
	}

	for field, context := range numericFieldTokens {

		if writer.dataStoreType == utils.Log && datastore.IsEventMetadataField(field) {

			utils.IndexWriterRequests[utils.GetFastModN(utils.GetHash64([]byte(datastore.EventSearchPlugin+field)), utils.IndexWriters)] <- utils.IndexEvent{

				Plugin: datastore.EventSearchPlugin, StoreType: writer.dataStoreType, Tick: writer.tick, LookupEvent: true, Column: field, NumericContext: context, StringContext: nil,
			}
		} else {

			utils.IndexWriterRequests[utils.GetFastModN(utils.GetHash64([]byte(writer.plugin+field)), utils.IndexWriters)] <- utils.IndexEvent{

				Plugin: writer.plugin, StoreType: writer.dataStoreType, Tick: writer.tick, LookupEvent: true, Column: field, NumericContext: context, StringContext: nil,
			}
		}

	}
}

func (writer *HorizontalWriter) handleStringError(blobPoolIndex int, err error, keyBytes []byte, storeName string) (int, []byte, error, bool) {

	if blobPoolIndex != utils.NotAvailable {

		writer.encoder.MemoryPool.ReleaseBytePool(blobPoolIndex)

		blobPoolIndex = utils.NotAvailable
	}

	horizontalWriterLogger.Error(fmt.Sprintf("err: %v occurred while decoding key: %v for store: %v for writerId : %v", err.Error(), string(keyBytes), storeName, writer.writerId))

	return -1, nil, err, false
}

func (writer *HorizontalWriter) handleNumericError(blobPoolIndex int, err error, keyBytes []byte, storeName string) (int, []byte, error) {

	if blobPoolIndex != utils.NotAvailable {

		writer.encoder.MemoryPool.ReleaseBytePool(blobPoolIndex)

		blobPoolIndex = utils.NotAvailable
	}

	horizontalWriterLogger.Error(fmt.Sprintf("err: %v occurred while decoding key: %v for store: %v for writerId : %v", err.Error(), string(keyBytes), storeName, writer.writerId))

	return -1, nil, err
}

func (writer *HorizontalWriter) read(fileName string) error {

	if utils.TraceEnabled() {

		horizontalWriterLogger.Trace(fmt.Sprintf("writer %v started reading file %v", writer.writerId, fileName))
	}

	file, err := os.OpenFile(fileName, os.O_RDWR, 0666)

	defer func(file *os.File) {

		if file != nil {

			_ = file.Close()
		}

		if utils.TraceEnabled() {

			horizontalWriterLogger.Trace(fmt.Sprintf("writer %v deleting file %v", writer.writerId, fileName))
		}

		err = os.Remove(fileName)

		if err != nil {

			horizontalWriterLogger.Error(fmt.Sprintf("writer %v failed to delete file %v, reason: %v", writer.writerId, fileName, err))
		}
	}(file)

	if err != nil {

		horizontalWriterLogger.Error(fmt.Sprintf("writer %v failed to open the file %v, reason: %v", writer.writerId, fileName, err))

		return err
	}

	var stat os.FileInfo

	if stat, err = file.Stat(); err == nil && int(stat.Size()) > len(writer.valueBufferBytes) {

		writer.bufferBytes, err = os.ReadFile(fileName)

		return err
	}

	length, err := file.ReadAt(writer.valueBufferBytes, 0)

	if err != nil && !strings.EqualFold(err.Error(), "EOF") {

		return err
	}

	writer.bufferBytes = writer.valueBufferBytes[:length]

	return nil

}

func (writer *HorizontalWriter) readStringColumn() (result string) {

	writer.length = int(binary.LittleEndian.Uint32(writer.bufferBytes[writer.position : writer.position+4]))

	writer.position += 4

	result = string(writer.bufferBytes[writer.position : writer.position+writer.length])

	writer.position += writer.length

	return result

}

func (writer *HorizontalWriter) appendMissingColumn(dataType byte, missingField string) {

	switch writer.getDataType(dataType, missingField) {

	case datastore.IntegerColumn:

		if _, ok := writer.int64Fields[missingField]; !ok {

			writer.int32Fields[missingField] = append(writer.int32Fields[missingField], 0)

			if _, found := writer.int32Fields[missingField+utils.MaxSuffix]; !found {

				writer.int32Fields[missingField+utils.MaxSuffix] = append(writer.int32Fields[missingField+utils.MaxSuffix], 0)
			}

		} else {

			writer.int64Fields[missingField] = append(writer.int64Fields[missingField], 0)

			if _, found := writer.int64Fields[missingField+utils.MaxSuffix]; !found {

				writer.int64Fields[missingField+utils.MaxSuffix] = append(writer.int64Fields[missingField+utils.MaxSuffix], 0)
			}
		}

	case datastore.StringColumn:

		writer.stringFields[missingField] = append(writer.stringFields[missingField], utils.Empty)

	}

}

func (writer *HorizontalWriter) updateIndexableColumns() {

	switch writer.dataStoreType {

	case utils.StatusFlapHistory:

		writer.plugin = writer.plugin + utils.DotSeparator + datastore.Duration

		if !datastore.IsIndexablePlugin(writer.plugin) {

			datastore.AlterIndexableColumns(writer.plugin, map[string]interface{}{

				utils.ObjectId: utils.Empty,

				datastore.Instance: utils.Empty,

				datastore.ObjectStatusFlapHistory: utils.Empty,
			}, utils.Add)
		}

	case utils.CorrelationMetric:

		if !datastore.IsIndexablePlugin(writer.plugin) {

			datastore.AlterIndexableColumns(writer.plugin, map[string]interface{}{

				utils.EventSource: utils.Empty,
			}, utils.Add)
		}

	case utils.Compliance:

		if !datastore.IsIndexableColumn(datastore.CompliancePlugin, utils.CompliancePolicyID) {

			datastore.AlterIndexableColumns(writer.plugin, map[string]interface{}{
				utils.CompliancePolicyID: utils.Empty,
			}, utils.Replace)

		}
	}
}
