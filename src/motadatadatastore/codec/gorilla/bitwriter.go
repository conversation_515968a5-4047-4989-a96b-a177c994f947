/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package gorilla

import (
	"io"
)

// A bit is a zero or a one
type bit bool

const (
	// Zero is our exported type for '0' bits
	Zero bit = false
	// One is our exported type for '1' bits
	One bit = true
)

// A bitWriter writes bits to an io.Writer
type bitWriter struct {
	writer io.Writer

	bytes [1]byte

	count uint8
}

// newWriter returns a bitWriter that buffers bits and write the resulting bytes to 'writer'
func newWriter(writer io.Writer) *bitWriter {

	bitWriter := new(bitWriter)

	bitWriter.writer = writer

	bitWriter.count = 8

	return bitWriter
}

//func (writer *bitWriter) Pending() (byteValue byte, values uint8) {
//
//	return writer.bytes[0], writer.count
//}

func (writer *bitWriter) Resume(byteValue byte, count uint8) {

	writer.bytes[0] = byteValue

	writer.count = count
}

// writeBit writes a single bit to the stream, writing a new byte to 'writer' if required.
func (writer *bitWriter) writeBit(bit bit) error {

	if bit {

		writer.bytes[0] |= 1 << (writer.count - 1)
	}

	writer.count--

	if writer.count == 0 {

		if usedBits, err := writer.writer.Write(writer.bytes[:]); usedBits != 1 || err != nil {

			return err
		}

		writer.bytes[0] = 0

		writer.count = 8
	}

	return nil
}

// WriteByte writes a single byte to the stream, regardless of alignment
func (writer *bitWriter) WriteByte(byteValue byte) error {

	// fill up bytes.bytes with bytes.count bits from byteValue
	writer.bytes[0] |= byteValue >> (8 - writer.count)

	if usedBits, err := writer.writer.Write(writer.bytes[:]); usedBits != 1 || err != nil {
		return err
	}
	writer.bytes[0] = byteValue << writer.count

	return nil
}

// Flush empties the currently in-process byte by filling it with 'bit'.
func (writer *bitWriter) Flush(bit bit) error {

	for writer.count != 8 {

		err := writer.writeBit(bit)

		if err != nil {

			return err
		}
	}

	return nil
}

// writeBits writes the bits least significant bits of u, most-significant-bit first.
func (writer *bitWriter) writeBits(value uint64, bits int) error {

	value <<= 64 - uint(bits)
	for bits >= 8 {

		byteValue := byte(value >> 56)

		err := writer.WriteByte(byteValue)

		if err != nil {
			return err
		}

		value <<= 8

		bits -= 8
	}

	for bits > 0 {

		err := writer.writeBit((value >> 63) == 1)

		if err != nil {

			return err
		}

		value <<= 1

		bits--
	}

	return nil
}

// Reset replaces the underlying io.Writer with the provided writer and resets
// all internal state to its initial value allowing for reuse of the bitWriter
// without additional allocations
//func (writer *bitWriter) Reset(ioWriter io.Writer) {
//
//	writer.writer = ioWriter
//
//	writer.bytes[0] = 0x00
//
//	writer.count = 8
//}
